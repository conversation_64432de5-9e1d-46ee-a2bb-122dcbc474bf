<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>SVM 2D Training Visualizer — Integrated</title>
  <style>
    :root { --bg:#0f172a; --panel:#0b1220; --text:#e5e7eb; --muted:#9ca3af; --accent:#22c55e; --accent2:#60a5fa; --mag:#a78bfa; --danger:#f87171; --warning:#fbbf24; }
    *{box-sizing:border-box}
    body{margin:0;font-family:ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto,Ubuntu;color:var(--text);background:radial-gradient(1000px 600px at 10% -10%,#1f2937,var(--bg))}
    .wrap{max-width:1200px;margin:24px auto;padding:16px}
    .grid{display:grid;grid-template-columns:1.2fr .8fr;gap:16px}
    .card{background:linear-gradient(180deg,rgba(255,255,255,.04),rgba(255,255,255,.02));border:1px solid rgba(255,255,255,.07);border-radius:16px;box-shadow:0 10px 30px rgba(0,0,0,.25)}
    .card h2{margin:0;padding:14px 16px;border-bottom:1px solid rgba(255,255,255,.06);font-size:18px;font-weight:600;color:#f8fafc}
    .card .body{padding:16px}

    .canvases{display:grid;gap:12px}
    canvas{width:100%;height:420px;background:#0a1120;border:1px solid rgba(255,255,255,.06);border-radius:16px}
    #lossCanvas{height:180px}

    .legend{display:flex;gap:16px;font-size:13px;color:var(--muted);margin-top:8px}
    .dot{display:inline-block;width:10px;height:10px;border-radius:50%;margin-right:6px}
    .dot.cneg{background:#60a5fa}
    .dot.cpos{background:#f472b6}
    .dot.line{background:var(--accent)}
    .dot.prev{background:var(--mag)}

    .equation{margin-top:10px;font-family:ui-monospace,Consolas,Menlo,monospace;color:#c7d2fe;background:var(--panel);border:1px solid rgba(255,255,255,.08);padding:10px 12px;border-radius:10px}

    .controls{display:grid;grid-template-columns:1fr 1fr;gap:12px}
    .row{display:grid;grid-template-columns:130px 1fr 110px;gap:10px;align-items:center}
    label{color:var(--muted);font-size:12px}
    input[type=number],input[type=range]{width:100%;background:#0b1220;color:#e5e7eb;border:1px solid rgba(255,255,255,.08);border-radius:10px;padding:8px 10px}
    input[type=range]{appearance:none;height:6px;border-radius:999px;background:#222a3b}
    input[type=range]::-webkit-slider-thumb{appearance:none;width:16px;height:16px;border-radius:999px;background:var(--accent2);border:2px solid white;box-shadow:0 1px 4px rgba(0,0,0,.4)}
    .btns{display:flex;gap:10px;flex-wrap:wrap;margin-top:8px}
    button{border:1px solid rgba(255,255,255,.12);color:#e5e7eb;background:#101626;padding:10px 14px;border-radius:12px;cursor:pointer;font-weight:600;letter-spacing:.2px;transition:transform .06s ease,background .2s ease,border-color .2s ease}
    button:hover{transform:translateY(-1px);border-color:rgba(255,255,255,.22)}
    .primary{background:linear-gradient(135deg,#1d4ed8,#22c55e);border:none}
    .danger{background:linear-gradient(135deg,#991b1b,#ef4444);border:none}
    .warn{background:linear-gradient(135deg,#92400e,#f59e0b);border:none}
    .muted{background:#0b1220}

    .status{display:grid;grid-template-columns:repeat(3,1fr);gap:10px;margin-top:12px}
    .stat{background:#0b1220;border:1px solid rgba(255,255,255,.08);border-radius:12px;padding:12px}
    .stat .k{color:var(--muted);font-size:12px}
    .stat .v{font-size:18px;font-weight:700}

    .mode{display:flex;gap:8px;align-items:center;margin:8px 0 10px}
    .badge{display:inline-block;padding:4px 8px;border-radius:999px;font-size:12px;border:1px solid rgba(255,255,255,.15)}
    .phase-0{background:#0b3b1d;border-color:#1f7a3e}
    .phase-1{background:#38210b;border-color:#8a5a10}
    .phase-2{background:#2a223f;border-color:#6b5ca3}
    .phase-3{background:#401d2b;border-color:#b0476c}

    .calc-grid{display:grid;grid-template-columns:1fr;gap:10px}
    .calc-box{background:#0b1220;border:1px solid rgba(255,255,255,.1);border-radius:12px;padding:10px}
    .calc-box h3{margin:0 0 6px 0;font-size:14px;color:#dbeafe}
    .mono{font-family:ui-monospace,Consolas,Menlo,monospace;white-space:pre-wrap}
    .scroll{max-height:240px;overflow:auto}

    @media (max-width:980px){.grid{grid-template-columns:1fr}}
  </style>
</head>
<body>
  <div class="wrap">
    <div class="grid">
      <!-- Viz -->
      <div class="card">
        <h2>SVM (2D) — Training Playground <span id="phaseBadge" class="badge phase-0" style="margin-left:8px">Classic mode</span></h2>
        <div class="body">
          <div class="canvases">
            <canvas id="plotCanvas"></canvas>
            <canvas id="lossCanvas"></canvas>
          </div>
          <div class="legend">
            <span><span class="dot cneg"></span> Class −1</span>
            <span><span class="dot cpos"></span> Class +1</span>
            <span><span class="dot line"></span> Decision & margins</span>
            <span><span class="dot prev"></span> Previous boundary</span>
          </div>
          <div class="equation" id="equation">J = 0.5‖w‖² + C·⟨hinge⟩,  hinge = max(0, 1 − y·(w·x + b))</div>
        </div>
      </div>

      <!-- Controls -->
      <div class="card">
        <h2>Controls</h2>
        <div class="body">
          <div class="mode">
            <label style="color:var(--muted);font-size:12px">Mode:</label>
            <button id="modeClassic" class="muted">Classic</button>
            <button id="modePhased" class="muted">Multi‑phase</button>
          </div>
          <div class="controls">
            <div class="row"><label># Points</label><input id="points" type="range" min="40" max="2000" value="400" step="20"/><input id="pointsNum" type="number" min="40" max="2000" value="400"/></div>
            <div class="row"><label>Noise σ</label><input id="noise" type="range" min="0" max="3" value="0.6" step="0.01"/><input id="noiseNum" type="number" min="0" max="3" value="0.6" step="0.01"/></div>
            <div class="row"><label>True w1*</label><input id="trueW1" type="range" min="-4" max="4" value="1.2" step="0.1"/><input id="trueW1Num" type="number" min="-4" max="4" value="1.2" step="0.1"/></div>
            <div class="row"><label>True w2*</label><input id="trueW2" type="range" min="-4" max="4" value="-2.0" step="0.1"/><input id="trueW2Num" type="number" min="-4" max="4" value="-2.0" step="0.1"/></div>
            <div class="row"><label>True b*</label><input id="trueB" type="range" min="-6" max="6" value="0.2" step="0.1"/><input id="trueBNum" type="number" min="-6" max="6" value="0.2" step="0.1"/></div>
            <div class="row"><label>Learning rate η</label><input id="lr" type="range" min="0.0005" max="0.5" value="0.05" step="0.0005"/><input id="lrNum" type="number" min="0.0005" max="0.5" value="0.05" step="0.0005"/></div>
            <div class="row"><label>Epochs</label><input id="epochs" type="range" min="1" max="2000" value="300" step="1"/><input id="epochsNum" type="number" min="1" max="2000" value="300" step="1"/></div>
            <div class="row"><label>Batch size</label><input id="batch" type="range" min="1" max="2000" value="400" step="1"/><input id="batchNum" type="number" min="1" max="2000" value="400" step="1"/></div>
            <div class="row"><label>Penalty C</label><input id="C" type="range" min="0.01" max="10" value="1" step="0.01"/><input id="CNum" type="number" min="0.01" max="10" value="1" step="0.01"/></div>
          </div>
          <div class="btns">
            <button class="primary" id="btnStart">Start</button>
            <button class="muted" id="btnStep">Step</button>
            <button class="muted" id="btnNextPhase" style="display:none">Next Phase</button>
            <button class="warn" id="btnShuffle">Shuffle Data</button>
            <button class="danger" id="btnReset">Reset</button>
            <button class="muted" id="btnRegenerate">Regenerate Dataset</button>
          </div>
          <div class="status">
            <div class="stat"><div class="k">Epoch</div><div class="v" id="statEpoch">0</div></div>
            <div class="stat"><div class="k">Objective J</div><div class="v" id="statLoss">—</div></div>
            <div class="stat"><div class="k">w = (w1,w2), b</div><div class="v" id="statWB">(0.00, 0.00), 0.00</div></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Calculations (Multi‑phase) -->
    <div id="calcCard" class="card" style="margin-top:16px; display:none">
      <h2>Live Calculations</h2>
      <div class="body calc-grid">
        <div class="calc-box"><h3 id="phaseTitle">Phase 1 — Evaluate margins & hinge</h3><div class="mono" id="calcMain">—</div></div>
        <div class="calc-box"><h3>Mini‑batch table (first 60)</h3><div class="mono scroll" id="tableBox">—</div></div>
      </div>
    </div>
  </div>

  <script>
    // ===== Utilities =====
    const el=(id)=>document.getElementById(id);
    const RNG=(seed=1234567)=>{let s=seed>>>0;return()=>{s=(s*1664525+1013904223)>>>0;return s/0xffffffff;}};
    const bindRangePair=(rangeEl,numEl,onChange)=>{const sync=(src,dst)=>{dst.value=src.value;onChange(parseFloat(src.value));};rangeEl.addEventListener('input',()=>sync(rangeEl,numEl));numEl.addEventListener('input',()=>sync(numEl,rangeEl));onChange(parseFloat(rangeEl.value));};

    // ===== State =====
    const state={
      seed:42,
      data:{x1:[],x2:[],y:[]}, // y in {-1,+1}
      true:{w1:1.2,w2:-2.0,b:0.2,sigma:0.6},
      model:{w1:0,w2:0,b:0},
      train:{lr:0.05,epochs:300,batch:400,epoch:0,running:false,C:1.0},
      history:[],
      scale:{x1min:-1,x1max:1,x2min:-1,x2max:1},
      mode:'classic', // 'classic'|'phased'
      phase:0,
      prevLine:null, // {w1,w2,b}
      batchCache:null
    };

    // ===== Data generation =====
    function gaussian(rand){let u=0,v=0;while(u===0)u=rand();while(v===0)v=rand();return Math.sqrt(-2*Math.log(u))*Math.cos(2*Math.PI*v)}
    function sign(z){return z>=0?1:-1}

    function generateData(n=400){
      const rand=RNG(state.seed);
      const x1=new Array(n).fill(0).map(()=> (rand()*10-5));
      const x2=x1.map(v=> (rand()*10-5));
      // linear boundary with noise
      const y=new Array(n).fill(0).map((_,i)=> {
        const margin = state.true.w1*x1[i] + state.true.w2*x2[i] + state.true.b + gaussian(rand)*state.true.sigma;
        return sign(margin);
      });
      state.data={x1,x2,y};
      fitScale();
      state.history=[]; state.train.epoch=0; state.prevLine=null; state.phase=0; state.batchCache=null;
      updateStats(0,state.model); drawAll(); updateEquation();
    }

    function fitScale(){
      const {x1,x2}=state.data; const pad=.15;
      const x1min=Math.min(...x1),x1max=Math.max(...x1); const x2min=Math.min(...x2),x2max=Math.max(...x2);
      const xr=x1max-x1min, yr=x2max-x2min;
      state.scale={x1min:x1min-xr*pad,x1max:x1max+xr*pad,x2min:x2min-yr*pad,x2max:x2max+yr*pad};
    }

    // ===== Model / Loss / Grads =====
    const decision=(x1,x2,w1=state.model.w1,w2=state.model.w2,b=state.model.b)=> w1*x1 + w2*x2 + b;

    function objective(xs1,xs2,ys){ // 0.5 ||w||^2 + C * mean(hinge)
      const n=ys.length; let sumH=0; for(let i=0;i<n;i++){ const m=ys[i]*decision(xs1[i],xs2[i]); const h=Math.max(0,1-m); sumH+=h; }
      const reg=0.5*(state.model.w1*state.model.w1 + state.model.w2*state.model.w2);
      return reg + state.train.C*(sumH/n);
    }

    function grads(xs1,xs2,ys){
      const n=ys.length; let gW1=state.model.w1, gW2=state.model.w2, gB=0; // reg part
      const rows=[]; let sumYX1=0,sumYX2=0,sumY=0,active=0;
      for(let i=0;i<n;i++){
        const x1=xs1[i], x2=xs2[i], y=ys[i]; const m=y*decision(x1,x2); const h=Math.max(0,1-m); const I=h>0?1:0;
        if(I){ sumYX1 += y*x1; sumYX2 += y*x2; sumY += y; active++; gW1 += - (state.train.C * (y*x1) / n); gW2 += - (state.train.C * (y*x2) / n); gB += - (state.train.C * (y) / n); }
        if(i<60){ rows.push({x1,x2,y,f:decision(x1,x2),m,hinge:h,I,contribW1: I? -(y*x1)/n : 0, contribW2: I? -(y*x2)/n : 0, contribB: I? -(y)/n : 0}); }
      }
      return { dW1:gW1, dW2:gW2, dB:gB, rows, active, sumYX1, sumYX2, sumY };
    }

    // ===== Classic step =====
    function stepClassic(){ const {x1,x2,y}=state.data; const n=y.length; const b=Math.max(1,Math.min(state.train.batch,n)); const idx=[...Array(n).keys()]; for(let i=n-1;i>0;i--){const j=Math.floor(Math.random()*(i+1));[idx[i],idx[j]]=[idx[j],idx[i]];} const xs1=idx.slice(0,b).map(i=>x1[i]); const xs2=idx.slice(0,b).map(i=>x2[i]); const ys=idx.slice(0,b).map(i=>y[i]); const {dW1,dW2,dB}=grads(xs1,xs2,ys); state.prevLine={...state.model}; state.model.w1 -= state.train.lr*dW1; state.model.w2 -= state.train.lr*dW2; state.model.b -= state.train.lr*dB; const obj=objective(x1,x2,y); state.history.push(obj); state.train.epoch+=1; updateStats(obj,state.model); drawAll(); if(state.train.epoch>=state.train.epochs) state.train.running=false; }

    // ===== Phased step engine =====
    function prepareBatch(){ const {x1,x2,y}=state.data; const n=y.length; const b=Math.max(1,Math.min(state.train.batch,n)); const idx=[...Array(n).keys()]; for(let i=n-1;i>0;i--){const j=Math.floor(Math.random()*(i+1));[idx[i],idx[j]]=[idx[j],idx[i]];} const xs1=idx.slice(0,b).map(i=>x1[i]); const xs2=idx.slice(0,b).map(i=>x2[i]); const ys=idx.slice(0,b).map(i=>y[i]); state.batchCache={xs1,xs2,ys,f:null,m:null,hinge:null,rows:null,dW1:null,dW2:null,dB:null,active:0,sumYX1:0,sumYX2:0,sumY:0}; }

    function nextPhase(){ switch(state.phase){ case 0: if(!state.batchCache) prepareBatch(); const {xs1,xs2,ys}=state.batchCache; const f=xs1.map((v,i)=>decision(v,xs2[i])); const m=f.map((fv,i)=> ys[i]*fv ); const hinge=m.map(v=>Math.max(0,1-v)); Object.assign(state.batchCache,{f,m,hinge}); renderPhase(0,{xs1,xs2,ys,f,m,hinge}); state.phase=1; break; case 1: const {f:ff,m:mm,hinge:hh,xs1:bx1,xs2:bx2,ys:by}=state.batchCache; let active=0,sumYX1=0,sumYX2=0,sumY=0; const rows=[]; const n=bx1.length; for(let i=0;i<n;i++){ const I=hh[i]>0?1:0; if(I){ active++; sumYX1+=by[i]*bx1[i]; sumYX2+=by[i]*bx2[i]; sumY+=by[i]; } if(i<60){ rows.push({x1:bx1[i],x2:bx2[i],y:by[i],f:ff[i],m:mm[i],hinge:hh[i],I,contribW1: I? -(by[i]*bx1[i])/n : 0, contribW2: I? -(by[i]*bx2[i])/n : 0, contribB: I? -(by[i])/n : 0}); } } Object.assign(state.batchCache,{rows,active,sumYX1,sumYX2,sumY}); renderPhase(1,{active,sumYX1,sumYX2,sumY,rows}); state.phase=2; break; case 2: const n2=state.batchCache.xs1.length; const dW1 = state.model.w1 + state.train.C * ( - state.batchCache.sumYX1 / n2 ); const dW2 = state.model.w2 + state.train.C * ( - state.batchCache.sumYX2 / n2 ); const dB = state.train.C * ( - state.batchCache.sumY / n2 ); Object.assign(state.batchCache,{dW1,dW2,dB}); renderPhase(2,{n:n2,dW1,dW2,dB,eta:state.train.lr,C:state.train.C}); state.phase=3; break; case 3: state.prevLine={...state.model}; state.model.w1 -= state.train.lr*state.batchCache.dW1; state.model.w2 -= state.train.lr*state.batchCache.dW2; state.model.b  -= state.train.lr*state.batchCache.dB; const obj=objective(state.data.x1,state.data.x2,state.data.y); state.history.push(obj); state.train.epoch+=1; updateStats(obj,state.model); drawAll(); renderPhase(3,{obj}); state.phase=0; state.batchCache=null; break;} updatePhaseUI(); }

    // ===== Rendering =====
    const plot=el('plotCanvas'); const loss=el('lossCanvas'); const pctx=plot.getContext('2d'); const lctx=loss.getContext('2d');
    function mapX1(x){const {x1min,x1max}=state.scale; return (x-x1min)/(x1max-x1min)*(plot.width-40)+20}
    function mapX2(y){const {x2min,x2max}=state.scale; return (1-(y-x2min)/(x2max-x2min))*(plot.height-40)+20}

    function drawGrid(ctx,w,h){ctx.save();ctx.strokeStyle='rgba(255,255,255,.06)';ctx.lineWidth=1;for(let x=40;x<w;x+=40){ctx.beginPath();ctx.moveTo(x,0);ctx.lineTo(x,h);ctx.stroke();}for(let y=40;y<h;y+=40){ctx.beginPath();ctx.moveTo(0,y);ctx.lineTo(w,y);ctx.stroke();}ctx.restore();}
    function clearCanvas(c){const ctx=c.getContext('2d'); ctx.clearRect(0,0,c.width,c.height);}
    function resizeCanvases(){const dpr=window.devicePixelRatio||1; [plot,loss].forEach(c=>{const rect=c.getBoundingClientRect(); c.width=Math.floor(rect.width*dpr); c.height=Math.floor(rect.height*dpr); c.getContext('2d').setTransform(dpr,0,0,dpr,0,0);});}

    function drawAll(){ resizeCanvases(); drawScatterAndBoundary(); drawLoss(); updateEquation(); }

    function drawLineAndMargins(w1,w2,b,styleWidth,styleColor, dashed=false){
      const {x1min,x1max,x2min,x2max}=state.scale; const drawIso=(k)=>{ // line w1*x1 + w2*x2 + b = k
        if(Math.abs(w2) > 1e-8){
          const yL = (-b - w1*x1min + k)/w2; const yR = (-b - w1*x1max + k)/w2;
          pctx.beginPath(); pctx.moveTo(mapX1(x1min), mapX2(yL)); pctx.lineTo(mapX1(x1max), mapX2(yR)); pctx.stroke();
        } else if(Math.abs(w1) > 1e-8){
          const xV = (-b + k)/w1; const xP = mapX1(xV); pctx.beginPath(); pctx.moveTo(xP, mapX2(x2min)); pctx.lineTo(xP, mapX2(x2max)); pctx.stroke();
        }
      };
      pctx.strokeStyle=styleColor; pctx.lineWidth=styleWidth; if(dashed) pctx.setLineDash([6,6]); drawIso(0); drawIso(1); drawIso(-1); pctx.setLineDash([]);
    }

    function drawScatterAndBoundary(){ clearCanvas(plot); const w=plot.width/(window.devicePixelRatio||1); const h=plot.height/(window.devicePixelRatio||1); drawGrid(pctx,w,h); pctx.strokeStyle='rgba(255,255,255,.25)'; pctx.lineWidth=1.5; pctx.strokeRect(20,20,w-40,h-40);
      // points
      const N=state.data.y.length; for(let i=0;i<N;i++){ const xi1=state.data.x1[i], xi2=state.data.x2[i], yi=state.data.y[i]; pctx.beginPath(); pctx.arc(mapX1(xi1), mapX2(xi2), 3, 0, Math.PI*2); pctx.fillStyle = yi>0? '#f472b6' : '#60a5fa'; pctx.fill(); }
      // previous & current boundaries with margins
      if(state.prevLine){ drawLineAndMargins(state.prevLine.w1,state.prevLine.w2,state.prevLine.b,1.5,'#a78bfa',true); }
      drawLineAndMargins(state.model.w1,state.model.w2,state.model.b,2.5,'#22c55e',false);
    }

    function drawLoss(){ clearCanvas(loss); const w=loss.width/(window.devicePixelRatio||1); const h=loss.height/(window.devicePixelRatio||1); drawGrid(lctx,w,h); lctx.strokeStyle='rgba(255,255,255,.25)'; lctx.lineWidth=1.5; lctx.strokeRect(20,20,w-40,h-40); if(state.history.length===0) return; const padL=30,padR=20,padT=20,padB=30; const xmin=0,xmax=state.history.length-1; const ymin=Math.min(...state.history),ymax=Math.max(...state.history); const mapLX=(x)=>padL+(x-xmin)/Math.max(1,(xmax-xmin))*(w-padL-padR); const mapLY=(y)=>padT+(1-(y-ymin)/Math.max(1e-9,(ymax-ymin)))*(h-padT-padB); lctx.strokeStyle='var(--warning)'; lctx.lineWidth=2; lctx.beginPath(); lctx.moveTo(mapLX(0),mapLY(state.history[0])); for(let i=1;i<state.history.length;i++){ lctx.lineTo(mapLX(i),mapLY(state.history[i])); } lctx.stroke(); lctx.fillStyle='rgba(229,231,235,.9)'; lctx.font='12px ui-sans-serif,system-ui'; lctx.fillText('Epoch',w/2-18,h-8); lctx.save(); lctx.translate(8,h/2+20); lctx.rotate(-Math.PI/2); lctx.fillText('Objective J',0,0); lctx.restore(); }

    // ===== Equation & Stats =====
    function updateEquation(){ const {w1,w2,b}=state.model; const C=state.train.C.toFixed(3); el('equation').textContent=`J = 0.5‖w‖² + ${C}·⟨hinge⟩  with  w=(${w1.toFixed(4)}, ${w2.toFixed(4)}),  b=${b.toFixed(4)}`; }
    function updateStats(val,model){ el('statEpoch').textContent=state.train.epoch.toString(); el('statLoss').textContent=(isFinite(val)?val.toFixed(6):'—'); el('statWB').textContent=`(${model.w1.toFixed(3)}, ${model.w2.toFixed(3)}), ${model.b.toFixed(3)}`; }

    // ===== Calculations (UI) =====
    function renderTable(rows){ const header='i  x1\t\t x2\t\t y(±1)\t f=w·x+b\t margin=y·f\t hinge\t I(m<1)\t contribW1\t contribW2\t contribB\n' + '-'.repeat(122)+'\n'; const pad=(v)=> (Number.isFinite(v)?v.toFixed(6):'—').padStart(9,' '); const lines=rows.map((r,i)=>`${String(i).padStart(3,' ')} ${pad(r.x1)} ${pad(r.x2)} ${pad(r.y)} ${pad(r.f)} ${pad(r.m)} ${pad(r.hinge)} ${String(r.I).padStart(7,' ')} ${pad(r.contribW1)} ${pad(r.contribW2)} ${pad(r.contribB)}`); el('tableBox').textContent=header + lines.join('\n'); }

    function renderPhase(p,info){ const titles=['Phase 1 — Evaluate margins & hinge','Phase 2 — Indicators & sums','Phase 3 — Gradients & deltas','Phase 4 — Apply update']; el('phaseTitle').textContent=titles[p]; const fmt=(n)=>Number.isFinite(n)?n.toFixed(6):'—'; if(p===0){ const {xs1,xs2,ys,f,m,hinge}=info; el('calcMain').textContent=`For each: fᵢ=w·xᵢ+b,  mᵢ=yᵢ·fᵢ,  hinge=max(0,1−mᵢ)\nCurrent w=${fmt(state.model.w1)},${fmt(state.model.w2)}  b=${fmt(state.model.b)}`; const rows=[]; for(let i=0;i<Math.min(60,xs1.length);i++){ rows.push({x1:xs1[i],x2:xs2[i],y:ys[i],f:f[i],m:m[i],hinge:hinge[i],I:hinge[i]>0?1:0,contribW1:NaN,contribW2:NaN,contribB:NaN}); } renderTable(rows); } if(p===1){ const {active,sumYX1,sumYX2,sumY,rows}=info; el('calcMain').textContent=`Active (m<1): ${active}\nΣ y·x1 = ${fmt(sumYX1)}\nΣ y·x2 = ${fmt(sumYX2)}\nΣ y     = ${fmt(sumY)}`; renderTable(rows); } if(p===2){ const {n,dW1,dW2,dB,eta,C}=info; el('calcMain').textContent=`n=${n}\n∂J/∂w1 = w1 + C·(−Σ y·x1 / n) = ${fmt(dW1)}\n∂J/∂w2 = w2 + C·(−Σ y·x2 / n) = ${fmt(dW2)}\n∂J/∂b  = C·(−Σ y / n)       = ${fmt(dB)}\nη=${fmt(eta)}  C=${fmt(C)}`; } if(p===3){ el('calcMain').textContent=`Apply: w ← w − η∇w, b ← b − η∂J/∂b\nNew w=(${fmt(state.model.w1)}, ${fmt(state.model.w2)}), b=${fmt(state.model.b)}\nObjective J (full) = ${fmt(info.obj)}`; } }

    function updatePhaseUI(){ const badge=el('phaseBadge'); if(state.mode==='classic'){ badge.textContent='Classic mode'; badge.className='badge phase-0'; el('btnNextPhase').style.display='none'; el('calcCard').style.display='none'; } else { const titles=['Phase 1 — Evaluate margins & hinge','Phase 2 — Indicators & sums','Phase 3 — Gradients & deltas','Phase 4 — Apply update']; badge.textContent=titles[state.phase]; badge.className='badge phase-'+state.phase; el('btnNextPhase').style.display='inline-block'; el('calcCard').style.display='block'; } updateEquation(); drawAll(); }

    // ===== Events =====
    function setMode(mode){ state.mode=mode; state.phase=0; state.batchCache=null; updatePhaseUI(); }
    el('modeClassic').addEventListener('click',()=>setMode('classic'));
    el('modePhased').addEventListener('click',()=>setMode('phased'));

    el('btnRegenerate').addEventListener('click',()=>{ generateData(Math.round(parseFloat(el('points').value))); });
    el('btnShuffle').addEventListener('click',()=>{ state.seed=Math.floor(Math.random()*1e9); generateData(Math.round(parseFloat(el('points').value))); });
    el('btnReset').addEventListener('click',()=>{ state.model={w1:0,w2:0,b:0}; state.history=[]; state.train.epoch=0; state.prevLine=null; state.phase=0; state.batchCache=null; updateStats(0,state.model); drawAll(); updatePhaseUI(); el('calcMain').textContent='—'; el('tableBox').textContent='—'; });

    el('btnStart').addEventListener('click',(e)=>{ state.train.running=!state.train.running; e.target.textContent=state.train.running?'Pause':'Start'; if(state.train.running) trainLoop(); });
    el('btnStep').addEventListener('click',()=>{ if(!state.train.running){ if(state.mode==='classic'){ stepClassic(); } else { nextPhase(); } } });
    el('btnNextPhase').addEventListener('click',()=>{ nextPhase(); });

    function trainLoop(){ if(!state.train.running) return; if(state.mode==='classic'){ stepClassic(); } else { nextPhase(); } requestAnimationFrame(trainLoop); }

    // ===== Bind controls =====
    bindRangePair(el('points'), el('pointsNum'), v=>{ state.train.batch=Math.min(state.train.batch,Math.round(v)); el('batch').max=v; el('batchNum').max=v; });
    bindRangePair(el('noise'), el('noiseNum'), v=>{ state.true.sigma=v; });
    bindRangePair(el('trueW1'), el('trueW1Num'), v=>{ state.true.w1=v; });
    bindRangePair(el('trueW2'), el('trueW2Num'), v=>{ state.true.w2=v; });
    bindRangePair(el('trueB'), el('trueBNum'), v=>{ state.true.b=v; });
    bindRangePair(el('lr'), el('lrNum'), v=>{ state.train.lr=v; });
    bindRangePair(el('epochs'), el('epochsNum'), v=>{ state.train.epochs=Math.round(v); });
    bindRangePair(el('batch'), el('batchNum'), v=>{ state.train.batch=Math.max(1,Math.round(v)); });
    bindRangePair(el('C'), el('CNum'), v=>{ state.train.C = v; updateEquation(); });

    // ===== Init =====
    window.addEventListener('resize',()=>{ drawAll(); });
    generateData(400);
    updatePhaseUI();
    updateEquation();
  </script>
</body>
</html>
