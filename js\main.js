// main.js - shared helpers
document.addEventListener('DOMContentLoaded',()=>{
  // small helper to attach ease-of-life classes
  document.querySelectorAll('a.btn').forEach(a=>{
    a.addEventListener('click',()=>{});
  });
});

// Simple canvas helper used by demos
function createCanvas(container){
  const c = document.createElement('canvas');
  c.width = Math.min(840, container.clientWidth-20);
  c.height = 520;
  container.appendChild(c);
  return c.getContext('2d');
}

export { createCanvas };
