(function(){
  // elements
  const vizContainer = document.getElementById('neuron-viz');
  // input elements (3 inputs)
  const inputX1 = document.getElementById('input-x1');
  const inputX1Num = document.getElementById('input-x1-num');
  const inputX2 = document.getElementById('input-x2');
  const inputX2Num = document.getElementById('input-x2-num');
  const inputX3 = document.getElementById('input-x3');
  const inputX3Num = document.getElementById('input-x3-num');
  // weight elements (3 weights)
  const weightW1 = document.getElementById('weight-w1');
  const weightW1Num = document.getElementById('weight-w1-num');
  const weightW2 = document.getElementById('weight-w2');
  const weightW2Num = document.getElementById('weight-w2-num');
  const weightW3 = document.getElementById('weight-w3');
  const weightW3Num = document.getElementById('weight-w3-num');
  const biasB = document.getElementById('bias-b');
  const biasBNum = document.getElementById('bias-b-num');
  const activation = document.getElementById('activation');
  const lr = document.getElementById('lr');
  const targetEl = document.getElementById('target');
  const stepFwd = document.getElementById('step-forward');
  const stepBack = document.getElementById('step-back');
  const randBtn = document.getElementById('rand');
  const resetBtn = document.getElementById('reset');
  const mathBox = document.getElementById('math-box');

  // initial params
  // params: three inputs and three weights
  let params = {
    x: [parseFloat(inputX1.value), parseFloat(inputX2.value), parseFloat(inputX3.value)],
    w: [parseFloat(weightW1.value), parseFloat(weightW2.value), parseFloat(weightW3.value)],
    b: parseFloat(biasB.value), act: activation.value, lr: parseFloat(lr.value), t: parseFloat(targetEl.value)
  };

  // create SVG
  const SVG_NS = 'http://www.w3.org/2000/svg';
  const svg = document.createElementNS(SVG_NS,'svg');
  svg.setAttribute('width','100%'); svg.setAttribute('height','100%');
  vizContainer.appendChild(svg);

  // activation chart container
  const actBox = document.getElementById('activation-chart');
  const actSvg = document.createElementNS(SVG_NS,'svg'); actSvg.setAttribute('width','100%'); actSvg.setAttribute('height','100%'); actBox.appendChild(actSvg);
  // chart internal size
  const CH_W = Math.max(320, actBox.clientWidth); const CH_H = 140;
  actSvg.setAttribute('viewBox', `0 0 ${CH_W} ${CH_H}`);

  // helpers to compute activations
  function activationFn(name,z){ if(name==='tanh') return Math.tanh(z); if(name==='relu') return Math.max(0,z); if(name==='sigmoid') return 1/(1+Math.exp(-z)); return z; }

  function renderActivationCurve(){
    // clear
    while(actSvg.firstChild) actSvg.removeChild(actSvg.firstChild);
    // axes
    const axis = document.createElementNS(SVG_NS,'path'); axis.setAttribute('d', `M 20 ${CH_H-20} L ${CH_W-20} ${CH_H-20}`); axis.setAttribute('stroke','var(--muted)'); axis.setAttribute('stroke-width','1'); actSvg.appendChild(axis);
    // sample z range
    const zmin = -4, zmax = 4; const steps = 200;
    let pts = [];
    for(let i=0;i<=steps;i++){
      const t = i/steps; const z = zmin + (zmax - zmin)*t;
      const v = activationFn(params.act, z);
      // map z->x, v->y
      const x = 20 + (CH_W-40)* ( (z - zmin) / (zmax - zmin) );
      // normalize v range for display: for tanh [-1,1], sigmoid [0,1], relu [0,4] approx
      let vy = v;
      if(params.act==='tanh') vy = (v + 1)/2; // map to 0..1
      if(params.act==='sigmoid') vy = v; // already 0..1
      if(params.act==='relu') vy = Math.min(1, v/4);
      if(params.act==='linear') vy = Math.min(1, (v+4)/8);
      const y = CH_H-20 - vy * (CH_H-40);
      pts.push([x,y,z,v]);
    }
    // path
    const d = pts.map((p,i)=> (i===0? 'M ':'L ') + p[0].toFixed(2) + ' ' + p[1].toFixed(2)).join(' ');
    const path = document.createElementNS(SVG_NS,'path'); path.setAttribute('d', d); path.setAttribute('fill','none'); path.setAttribute('stroke','var(--accent)'); path.setAttribute('stroke-width','2'); actSvg.appendChild(path);
    // lightly draw zero line
    const z0x = 20 + (CH_W-40) * ((0 - zmin)/(zmax - zmin));
    const vline = document.createElementNS(SVG_NS,'line'); vline.setAttribute('x1',z0x); vline.setAttribute('x2',z0x); vline.setAttribute('y1',10); vline.setAttribute('y2',CH_H-10); vline.setAttribute('stroke','rgba(255,255,255,0.04)'); actSvg.appendChild(vline);
    // store sampling for markers
    actSvg._pts = pts;
  }

  function renderActivationMarkers(z,y){
    // remove previous markers
    Array.from(actSvg.querySelectorAll('.marker')).forEach(n=>n.remove());
    if(!actSvg._pts) return;
    // find nearest sample for z
    let nearest = actSvg._pts.reduce((best,p)=> Math.abs(p[2]-z) < Math.abs(best[2]-z)? p: best, actSvg._pts[0]);
    const zx = nearest[0], zy = nearest[1];
    // marker for z (vertical)
    const mz = document.createElementNS(SVG_NS,'circle'); mz.classList.add('marker'); mz.setAttribute('cx', zx); mz.setAttribute('cy', zy); mz.setAttribute('r',4); mz.setAttribute('fill','var(--good)'); actSvg.appendChild(mz);
    // label
    const lz = document.createElementNS(SVG_NS,'text'); lz.classList.add('marker'); lz.setAttribute('x', zx+8); lz.setAttribute('y', zy-8); lz.setAttribute('fill','var(--text)'); lz.setAttribute('font-size','12'); lz.setAttribute('font-family','monospace'); lz.textContent = `z=${z.toFixed(2)}`; actSvg.appendChild(lz);
    // marker for y (vertical position mapped)
    // compute y position in chart coordinate
    // reuse activationFn mapping from renderActivationCurve samples
    // map actual z to chart x
    const zmin = -4, zmax = 4;
    const xChart = 20 + (CH_W-40) * ((z - zmin)/(zmax - zmin));
    let v = activationFn(params.act, z);
    let vy = v;
    if(params.act==='tanh') vy = (v + 1)/2;
    if(params.act==='sigmoid') vy = v;
    if(params.act==='relu') vy = Math.min(1, v/4);
    if(params.act==='linear') vy = Math.min(1, (v+4)/8);
    const yChart = CH_H-20 - vy * (CH_H-40);
    const my = document.createElementNS(SVG_NS,'rect'); my.classList.add('marker'); my.setAttribute('x', xChart-2); my.setAttribute('y', yChart-2); my.setAttribute('width',4); my.setAttribute('height',4); my.setAttribute('fill','var(--accent-2)'); actSvg.appendChild(my);
    const ly = document.createElementNS(SVG_NS,'text'); ly.classList.add('marker'); ly.setAttribute('x', xChart+8); ly.setAttribute('y', yChart+6); ly.setAttribute('fill','var(--text)'); ly.setAttribute('font-size','12'); ly.setAttribute('font-family','monospace'); ly.textContent = `y=${y.toFixed(3)}`; actSvg.appendChild(ly);
  }

  // layout coordinates
  const W = vizContainer.clientWidth || 900; const H = vizContainer.clientHeight || 420;
  // use viewBox for scaling
  svg.setAttribute('viewBox', `0 0 ${W} ${H}`);

  // create groups
  const inputGroup = document.createElementNS(SVG_NS,'g');
  const sumGroup = document.createElementNS(SVG_NS,'g');
  const actGroup = document.createElementNS(SVG_NS,'g');
  const outGroup = document.createElementNS(SVG_NS,'g');
  svg.appendChild(inputGroup); svg.appendChild(sumGroup); svg.appendChild(actGroup); svg.appendChild(outGroup);

  // positions
  const inX = 100; const inYs = [H/2 - 80, H/2, H/2 + 80];
  const sumX = W/2 - 80, sumY = H/2;
  const actX = W/2 + 40, actY = H/2;
  const outX = W - 120, outY = H/2;

  // helper to make circle+label
  function makeNode(cx,cy,r,fill,text){
    const g = document.createElementNS(SVG_NS,'g');
    const c = document.createElementNS(SVG_NS,'circle'); c.setAttribute('cx',cx); c.setAttribute('cy',cy); c.setAttribute('r',r); c.setAttribute('fill',fill); c.setAttribute('stroke','rgba(0,0,0,0.2)'); c.setAttribute('stroke-width','2');
    const t = document.createElementNS(SVG_NS,'text'); t.setAttribute('x',cx); t.setAttribute('y',cy+6); t.setAttribute('fill','var(--ink)'); t.setAttribute('font-family','monospace'); t.setAttribute('font-size','14'); t.setAttribute('text-anchor','middle'); t.textContent = text;
    g.appendChild(c); g.appendChild(t);
    return {g,c,t};
  }

  const inNode1 = makeNode(inX,inYs[0],24,'#ffb3a7','x1'); inputGroup.appendChild(inNode1.g);
  const inNode2 = makeNode(inX,inYs[1],24,'#ffb3a7','x2'); inputGroup.appendChild(inNode2.g);
  const inNode3 = makeNode(inX,inYs[2],24,'#ffb3a7','x3'); inputGroup.appendChild(inNode3.g);
  const sumNode = makeNode(sumX,sumY,42,'#7fb3ff','Σ'); sumGroup.appendChild(sumNode.g);
  // small z label under sum
  const sumZLabel = document.createElementNS(SVG_NS,'text'); sumZLabel.setAttribute('x', sumX); sumZLabel.setAttribute('y', sumY + 68); sumZLabel.setAttribute('text-anchor','middle'); sumZLabel.setAttribute('fill','var(--text)'); sumZLabel.setAttribute('font-family','monospace'); sumZLabel.setAttribute('font-size','13'); svg.appendChild(sumZLabel);
  const actBlock = document.createElementNS(SVG_NS,'rect'); actBlock.setAttribute('x', actX - 10); actBlock.setAttribute('y', actY - 40); actBlock.setAttribute('width',120); actBlock.setAttribute('height',80); actBlock.setAttribute('rx',10); actBlock.setAttribute('fill','var(--panel)'); actBlock.setAttribute('stroke','var(--accent)'); actGroup.appendChild(actBlock);
  const actLabel = document.createElementNS(SVG_NS,'text'); actLabel.setAttribute('x', actX + 50); actLabel.setAttribute('y', actY); actLabel.setAttribute('text-anchor','middle'); actLabel.setAttribute('fill','var(--text)'); actLabel.setAttribute('font-family','monospace'); actLabel.setAttribute('font-size','14'); actLabel.textContent = 'f'; actGroup.appendChild(actLabel);
  const outNode = makeNode(outX,outY,34,'#ffd37a','y'); outGroup.appendChild(outNode.g);

  // edge paths for three inputs
  const edge1 = document.createElementNS(SVG_NS,'path'); edge1.setAttribute('fill','none'); edge1.setAttribute('stroke-linecap','round'); edge1.setAttribute('stroke-width','6'); svg.appendChild(edge1);
  const edge2 = document.createElementNS(SVG_NS,'path'); edge2.setAttribute('fill','none'); edge2.setAttribute('stroke-linecap','round'); edge2.setAttribute('stroke-width','6'); svg.appendChild(edge2);
  const edge3 = document.createElementNS(SVG_NS,'path'); edge3.setAttribute('fill','none'); edge3.setAttribute('stroke-linecap','round'); edge3.setAttribute('stroke-width','6'); svg.appendChild(edge3);
  // labels for weights and bias
  const wLabel1 = document.createElementNS(SVG_NS,'text'); const wLabel2 = document.createElementNS(SVG_NS,'text'); const wLabel3 = document.createElementNS(SVG_NS,'text');
  [wLabel1,wLabel2,wLabel3].forEach(l=>{ l.setAttribute('font-family','monospace'); l.setAttribute('font-size','13'); l.setAttribute('fill','var(--text)'); l.setAttribute('text-anchor','middle'); svg.appendChild(l); });
  const bLabel = document.createElementNS(SVG_NS,'text'); bLabel.setAttribute('font-family','monospace'); bLabel.setAttribute('font-size','13'); bLabel.setAttribute('fill','var(--text)'); bLabel.setAttribute('text-anchor','middle'); svg.appendChild(bLabel);

  // visual tween state per weight
  let vis = { W: [ {wA:0.5,wW:Math.abs(params.w[0])}, {wA:0.5,wW:Math.abs(params.w[1])}, {wA:0.5,wW:Math.abs(params.w[2])} ] };

  function updateViz(){
  // compute forward for 3 inputs
  const x1 = params.x[0], x2 = params.x[1], x3 = params.x[2];
  const w1 = params.w[0], w2 = params.w[1], w3 = params.w[2], b = params.b;
  const z = w1*x1 + w2*x2 + w3*x3 + b;
  let y = activationFn(params.act, z);

  // update input labels
  inNode1.t.textContent = `x1=${x1.toFixed(2)}`;
  inNode2.t.textContent = `x2=${x2.toFixed(2)}`;
  inNode3.t.textContent = `x3=${x3.toFixed(2)}`;
  sumZLabel.textContent = `z=${z.toFixed(3)}`;
  outNode.t.textContent = `y=${y.toFixed(3)}`;

  // edges paths (curves from each input to sum)
  const p1 = `M ${inX+24} ${inYs[0]} C ${inX+120} ${inYs[0]-40} ${sumX-36} ${sumY-40} ${sumX-36} ${sumY}`;
  const p2 = `M ${inX+24} ${inYs[1]} C ${inX+160} ${inYs[1]-20} ${sumX-36} ${sumY-10} ${sumX-36} ${sumY}`;
  const p3 = `M ${inX+24} ${inYs[2]} C ${inX+120} ${inYs[2]+40} ${sumX-36} ${sumY+40} ${sumX-36} ${sumY}`;
  edge1.setAttribute('d', p1); edge2.setAttribute('d', p2); edge3.setAttribute('d', p3);

  // color/width per weight
  const mags = [Math.min(1, Math.abs(w1)/3), Math.min(1, Math.abs(w2)/3), Math.min(1, Math.abs(w3)/3)];
  const stroke1 = w1>=0? `rgba(59,130,246,${vis.W[0].wA})` : `rgba(255,107,107,${vis.W[0].wA})`;
  const stroke2 = w2>=0? `rgba(59,130,246,${vis.W[1].wA})` : `rgba(255,107,107,${vis.W[1].wA})`;
  const stroke3 = w3>=0? `rgba(59,130,246,${vis.W[2].wA})` : `rgba(255,107,107,${vis.W[2].wA})`;
  edge1.setAttribute('stroke', stroke1); edge1.setAttribute('stroke-width', 2 + 6*vis.W[0].wW);
  edge2.setAttribute('stroke', stroke2); edge2.setAttribute('stroke-width', 2 + 6*vis.W[1].wW);
  edge3.setAttribute('stroke', stroke3); edge3.setAttribute('stroke-width', 2 + 6*vis.W[2].wW);

  // weight labels
  wLabel1.setAttribute('x', inX + 180); wLabel1.setAttribute('y', inYs[0]-36); wLabel1.textContent = `w1=${w1.toFixed(3)}`;
  wLabel2.setAttribute('x', inX + 200); wLabel2.setAttribute('y', inYs[1]-18); wLabel2.textContent = `w2=${w2.toFixed(3)}`;
  wLabel3.setAttribute('x', inX + 180); wLabel3.setAttribute('y', inYs[2]+36); wLabel3.textContent = `w3=${w3.toFixed(3)}`;
  bLabel.setAttribute('x', sumX+24); bLabel.setAttribute('y', sumY+70); bLabel.textContent = `b=${b.toFixed(3)}`;

  // math
  const txt = `x = [${x1.toFixed(3)}, ${x2.toFixed(3)}, ${x3.toFixed(3)}]\nz = w1*x1 + w2*x2 + w3*x3 + b = ${w1.toFixed(3)}*${x1.toFixed(3)} + ${w2.toFixed(3)}*${x2.toFixed(3)} + ${w3.toFixed(3)}*${x3.toFixed(3)} + ${b.toFixed(3)} = ${z.toFixed(6)}\n` +
        `activation = ${params.act}\ny = ${y.toFixed(6)}\n\nTarget t = ${params.t.toFixed(3)}`;
  mathBox.textContent = txt;

  // update activation chart markers
  renderActivationMarkers(z,y);
  }

  // simple easing
  function lerp(a,b,t){ return a + (b-a)*t; }
  function ease(t){ return 1 - Math.pow(1-t,3); }

  // animation loop to ease visual params per-weight
  let last = null;
  function anim(ts){ if(!last) last = ts; const dt = Math.min(40, ts-last); last = ts; const t = ease(Math.min(1, dt/120));
    for(let i=0;i<3;i++){ const targetA = 0.25 + 0.75*Math.min(1, Math.abs(params.w[i])/3); const targetW = Math.min(1, Math.abs(params.w[i])/3); vis.W[i].wA = lerp(vis.W[i].wA, targetA, t); vis.W[i].wW = lerp(vis.W[i].wW, targetW, t); }
    updateViz(); requestAnimationFrame(anim); }
  requestAnimationFrame(anim);

  // forward/backprop math and update
  function forward(){
    const x1 = params.x[0], x2 = params.x[1], x3 = params.x[2];
    const w1 = params.w[0], w2 = params.w[1], w3 = params.w[2], b = params.b;
    const z = w1*x1 + w2*x2 + w3*x3 + b;
    let y = activationFn(params.act, z);
    // compute derivative dy/dz for backprop
    let dydz = 1;
    if(params.act==='tanh') dydz = 1 - Math.tanh(z)*Math.tanh(z);
    if(params.act==='relu') dydz = z>0?1:0;
    if(params.act==='sigmoid'){ const s = 1/(1+Math.exp(-z)); dydz = s*(1-s); }
    params.last = {x:[x1,x2,x3],z,y,dydz};
    updateViz();
  }

  function backprop(){
    if(!params.last) return;
    const {x,z,y,dydz} = params.last; const t = params.t; const lrv = params.lr;
    const dLdy = 2*(y - t);
    const dLdz = dLdy * dydz;
    // gradients per weight
    const dLdw = [ dLdz * x[0], dLdz * x[1], dLdz * x[2] ];
    const dLdb = dLdz;
    // apply step per weight
    for(let i=0;i<3;i++){ params.w[i] -= lrv * dLdw[i]; }
    params.b -= lrv * dLdb;
    params.grads = {dLdy,dLdz,dLdw,dLdb};
    refreshMathBackprop();
    // reflect in UI numeric fields
    weightW1.value = params.w[0].toFixed(3); weightW2.value = params.w[1].toFixed(3); weightW3.value = params.w[2].toFixed(3);
    weightW1Num.value = params.w[0].toFixed(3); weightW2Num.value = params.w[1].toFixed(3); weightW3Num.value = params.w[2].toFixed(3);
    biasB.value = params.b.toFixed(3); biasBNum.value = params.b.toFixed(3);
    return params;
  }

  function refreshMathBackprop(){ if(!params.grads) return; const g = params.grads; let txt = '--- Backprop ---\n'; txt += `dL/dy = 2*(y-t) = ${g.dLdy.toFixed(6)}\n`; txt += `dL/dz = dL/dy * dy/dz = ${g.dLdz.toFixed(6)}\n`; txt += `dL/dw = [ ${g.dLdw.map(v=>v.toFixed(6)).join(', ')} ]\n`; txt += `dL/db = ${g.dLdb.toFixed(6)}\n`;
    mathBox.textContent = txt; }

  // wire controls
  // inputs two-way sync
  inputX1.addEventListener('input',()=>{ params.x[0] = parseFloat(inputX1.value); inputX1Num.value = inputX1.value; forward(); });
  inputX1Num.addEventListener('input',()=>{ params.x[0] = parseFloat(inputX1Num.value); inputX1.value = params.x[0]; forward(); });
  inputX2.addEventListener('input',()=>{ params.x[1] = parseFloat(inputX2.value); inputX2Num.value = inputX2.value; forward(); });
  inputX2Num.addEventListener('input',()=>{ params.x[1] = parseFloat(inputX2Num.value); inputX2.value = params.x[1]; forward(); });
  inputX3.addEventListener('input',()=>{ params.x[2] = parseFloat(inputX3.value); inputX3Num.value = inputX3.value; forward(); });
  inputX3Num.addEventListener('input',()=>{ params.x[2] = parseFloat(inputX3Num.value); inputX3.value = params.x[2]; forward(); });
  // weights two-way sync
  weightW1.addEventListener('input',()=>{ params.w[0] = parseFloat(weightW1.value); weightW1Num.value = weightW1.value; });
  weightW1Num.addEventListener('input',()=>{ params.w[0] = parseFloat(weightW1Num.value); weightW1.value = params.w[0]; });
  weightW2.addEventListener('input',()=>{ params.w[1] = parseFloat(weightW2.value); weightW2Num.value = weightW2.value; });
  weightW2Num.addEventListener('input',()=>{ params.w[1] = parseFloat(weightW2Num.value); weightW2.value = params.w[1]; });
  weightW3.addEventListener('input',()=>{ params.w[2] = parseFloat(weightW3.value); weightW3Num.value = weightW3.value; });
  weightW3Num.addEventListener('input',()=>{ params.w[2] = parseFloat(weightW3Num.value); weightW3.value = params.w[2]; });
  biasB.addEventListener('input',()=>{ params.b = parseFloat(biasB.value); biasBNum.value = biasB.value; });
  biasBNum.addEventListener('input',()=>{ params.b = parseFloat(biasBNum.value); biasB.value = params.b; });
  activation.addEventListener('change',()=>{ params.act = activation.value; forward(); });
  lr.addEventListener('input',()=>{ params.lr = parseFloat(lr.value); });
  targetEl.addEventListener('input',()=>{ params.t = parseFloat(targetEl.value); });

  stepFwd.addEventListener('click', ()=>{ forward(); });
  stepBack.addEventListener('click', ()=>{ backprop(); // reflect in UI handled in backprop
  });
  randBtn.addEventListener('click', ()=>{ params.w = [Math.random()*6-3, Math.random()*6-3, Math.random()*6-3]; params.b = (Math.random()*6-3); weightW1.value = params.w[0].toFixed(3); weightW2.value = params.w[1].toFixed(3); weightW3.value = params.w[2].toFixed(3); weightW1Num.value = weightW1.value; weightW2Num.value = weightW2.value; weightW3Num.value = weightW3.value; biasB.value = params.b.toFixed(3); biasBNum.value = biasB.value; forward(); });
  resetBtn.addEventListener('click', ()=>{ params.w = [0.8, -0.4, 0.2]; params.b = 0; weightW1.value = params.w[0]; weightW2.value = params.w[1]; weightW3.value = params.w[2]; weightW1Num.value = weightW1.value; weightW2Num.value = weightW2.value; weightW3Num.value = weightW3.value; biasB.value = params.b; biasBNum.value = biasB.value; forward(); });

  // init
  forward();
  // draw initial activation curve
  renderActivationCurve();
})();
