<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Linear Regression Training Visualizer — Integrated</title>
  <style>
    :root { --bg:#0f172a; --panel:#0b1220; --text:#e5e7eb; --muted:#9ca3af; --accent:#22c55e; --accent2:#60a5fa; --danger:#f87171; --warning:#fbbf24; }
    *{box-sizing:border-box}
    body{margin:0;font-family:ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto,Ubuntu;color:var(--text);background:radial-gradient(1000px 600px at 10% -10%,#1f2937,var(--bg))}
    .wrap{max-width:1200px;margin:24px auto;padding:16px}
    .grid{display:grid;grid-template-columns:1.2fr .8fr;gap:16px}
    .card{background:linear-gradient(180deg,rgba(255,255,255,.04),rgba(255,255,255,.02));border:1px solid rgba(255,255,255,.07);border-radius:16px;box-shadow:0 10px 30px rgba(0,0,0,.25)}
    .card h2{margin:0;padding:14px 16px;border-bottom:1px solid rgba(255,255,255,.06);font-size:18px;font-weight:600;color:#f8fafc}
    .card .body{padding:16px}

    .canvases{display:grid;gap:12px}
    canvas{width:100%;height:360px;background:#0a1120;border:1px solid rgba(255,255,255,.06);border-radius:16px}
    #lossCanvas{height:180px}

    .legend{display:flex;gap:16px;font-size:13px;color:var(--muted);margin-top:8px}
    .dot{display:inline-block;width:10px;height:10px;border-radius:50%;margin-right:6px}
    .dot.data{background:var(--accent2)}
    .dot.line{background:var(--accent)}
    .dot.prev{background:#a78bfa}

    .equation{margin-top:10px;font-family:ui-monospace,Consolas,Menlo,monospace;color:#c7d2fe;background:var(--panel);border:1px solid rgba(255,255,255,.08);padding:10px 12px;border-radius:10px}
    .foot{ text-align:center;color:var(--muted);font-size:12px;margin-top:10px }

    .controls{display:grid;grid-template-columns:1fr 1fr;gap:12px}
    .row{display:grid;grid-template-columns:130px 1fr 90px;gap:10px;align-items:center}
    label{color:var(--muted);font-size:12px}
    input[type=number],input[type=range]{width:100%;background:#0b1220;color:var(--text);border:1px solid rgba(255,255,255,.08);border-radius:10px;padding:8px 10px}
    input[type=range]{appearance:none;height:6px;border-radius:999px;background:#222a3b}
    input[type=range]::-webkit-slider-thumb{appearance:none;width:16px;height:16px;border-radius:999px;background:var(--accent2);border:2px solid white;box-shadow:0 1px 4px rgba(0,0,0,.4)}
    .btns{display:flex;gap:10px;flex-wrap:wrap;margin-top:8px}
    button{border:1px solid rgba(255,255,255,.12);color:#e5e7eb;background:#101626;padding:10px 14px;border-radius:12px;cursor:pointer;font-weight:600;letter-spacing:.2px;transition:transform .06s ease,background .2s ease,border-color .2s ease}
    button:hover{transform:translateY(-1px);border-color:rgba(255,255,255,.22)}
    .primary{background:linear-gradient(135deg,#1d4ed8,#22c55e);border:none}
    .danger{background:linear-gradient(135deg,#991b1b,#ef4444);border:none}
    .warn{background:linear-gradient(135deg,#92400e,#f59e0b);border:none}
    .muted{background:#0b1220}

    .status{display:grid;grid-template-columns:repeat(2,1fr);gap:10px;margin-top:12px}
    .stat{background:#0b1220;border:1px solid rgba(255,255,255,.08);border-radius:12px;padding:12px}
    .stat .k{color:var(--muted);font-size:12px}
    .stat .v{font-size:18px;font-weight:700}

    /* Mode + Phase */
    .mode{display:flex;gap:8px;align-items:center;margin:8px 0 10px}
    .badge{display:inline-block;padding:4px 8px;border-radius:999px;font-size:12px;border:1px solid rgba(255,255,255,.15)}
    .phase-0{background:#0b3b1d;border-color:#1f7a3e}
    .phase-1{background:#38210b;border-color:#8a5a10}
    .phase-2{background:#2a223f;border-color:#6b5ca3}
    .phase-3{background:#401d2b;border-color:#b0476c}

    /* Calculations */
    .calc-grid{display:grid;grid-template-columns:1fr;gap:10px}
    .calc-box{background:#0b1220;border:1px solid rgba(255,255,255,.1);border-radius:12px;padding:10px}
    .calc-box h3{margin:0 0 6px 0;font-size:14px;color:#dbeafe}
    .mono{font-family:ui-monospace,Consolas,Menlo,monospace;white-space:pre-wrap}
    .scroll{max-height:220px;overflow:auto}

    @media (max-width:980px){.grid{grid-template-columns:1fr}}
  </style>
</head>
<body>
  <div class="wrap">
    <div class="grid">
      <!-- Visualization Panel -->
      <div class="card">
        <h2>Training Playground <span id="phaseBadge" class="badge phase-0" style="margin-left:8px">Classic mode</span></h2>
        <div class="body">
          <div class="canvases">
            <canvas id="plotCanvas"></canvas>
            <canvas id="lossCanvas"></canvas>
          </div>
          <div class="legend">
            <span><span class="dot data"></span> Data points</span>
            <span><span class="dot line"></span> Current line y = m·x + b</span>
            <span><span class="dot prev"></span> Previous line (update preview)</span>
          </div>
          <div class="equation" id="equation">y = m·x + b</div>
          <div class="foot">Toggle between Classic and Multi‑phase step to see all calculations.</div>
        </div>
      </div>

      <!-- Controls Panel -->
      <div class="card">
        <h2>Controls</h2>
        <div class="body">
          <div class="mode">
            <label style="color:var(--muted);font-size:12px">Mode:</label>
            <button id="modeClassic" class="muted">Classic</button>
            <button id="modePhased" class="muted">Multi‑phase</button>
          </div>
          <div class="controls">
            <div class="row"><label># Points</label><input id="points" type="range" min="10" max="500" value="120" step="10"/><input id="pointsNum" type="number" min="10" max="500" value="120"/></div>
            <div class="row"><label>Noise σ</label><input id="noise" type="range" min="0" max="1" value="0.2" step="0.01"/><input id="noiseNum" type="number" min="0" max="1" value="0.2" step="0.01"/></div>
            <div class="row"><label>True slope m*</label><input id="trueM" type="range" min="-3" max="3" value="1.5" step="0.1"/><input id="trueMNum" type="number" min="-3" max="3" value="1.5" step="0.1"/></div>
            <div class="row"><label>True bias b*</label><input id="trueB" type="range" min="-5" max="5" value="0.5" step="0.1"/><input id="trueBNum" type="number" min="-5" max="5" value="0.5" step="0.1"/></div>
            <div class="row"><label>Learning rate</label><input id="lr" type="range" min="0.0005" max="0.5" value="0.05" step="0.0005"/><input id="lrNum" type="number" min="0.0005" max="0.5" value="0.05" step="0.0005"/></div>
            <div class="row"><label>Epochs</label><input id="epochs" type="range" min="1" max="2000" value="300" step="1"/><input id="epochsNum" type="number" min="1" max="2000" value="300" step="1"/></div>
            <div class="row"><label>Batch size</label><input id="batch" type="range" min="1" max="500" value="120" step="1"/><input id="batchNum" type="number" min="1" max="500" value="120" step="1"/></div>
          </div>
          <div class="btns">
            <button class="primary" id="btnStart">Start</button>
            <button class="muted" id="btnStep">Step</button>
            <button class="muted" id="btnNextPhase" style="display:none">Next Phase</button>
            <button class="warn" id="btnShuffle">Shuffle Data</button>
            <button class="danger" id="btnReset">Reset</button>
            <button class="muted" id="btnRegenerate">Regenerate Dataset</button>
          </div>
          <div class="status">
            <div class="stat"><div class="k">Epoch</div><div class="v" id="statEpoch">0</div></div>
            <div class="stat"><div class="k">Loss (MSE)</div><div class="v" id="statLoss">—</div></div>
            <div class="stat"><div class="k">m</div><div class="v" id="statM">0.00</div></div>
            <div class="stat"><div class="k">b</div><div class="v" id="statB">0.00</div></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Calculations Panel (shown in Multi‑phase mode) -->
    <div id="calcCard" class="card" style="margin-top:16px; display:none">
      <h2>Live Calculations</h2>
      <div class="body calc-grid">
        <div class="calc-box"><h3 id="phaseTitle">Phase 1 — Evaluate ŷ</h3><div class="mono" id="calcMain">—</div></div>
        <div class="calc-box"><h3>Mini‑batch table (first 50 rows)</h3><div class="mono scroll" id="tableBox">—</div></div>
      </div>
    </div>
  </div>

  <script>
    // ===== Utility =====
    const el = (id)=>document.getElementById(id);
    const RNG=(seed=1234567)=>{let s=seed>>>0;return()=>{s=(s*1664525+1013904223)>>>0;return s/0xffffffff;}};
    const bindRangePair=(rangeEl,numEl,onChange)=>{const sync=(src,dst)=>{dst.value=src.value;onChange(parseFloat(src.value));};rangeEl.addEventListener('input',()=>sync(rangeEl,numEl));numEl.addEventListener('input',()=>sync(numEl,rangeEl));onChange(parseFloat(rangeEl.value));};

    // ===== Global State =====
    const state={
      seed:42,
      data:{x:[],y:[]},
      true:{m:1.5,b:0.5,sigma:0.2},
      model:{m:0,b:0},
      train:{lr:0.05,epochs:300,batch:120,epoch:0,running:false},
      history:[],
      scale:{xmin:-1,xmax:1,ymin:-1,ymax:1},
      mode:'classic', // 'classic' | 'phased'
      phase:0,
      prevLine:null,
      batchCache:null
    };

    // ===== Data Generation =====
    function gaussian(rand){let u=0,v=0;while(u===0)u=rand();while(v===0)v=rand();return Math.sqrt(-2*Math.log(u))*Math.cos(2*Math.PI*v)}
    function generateData(n=120){const rand=RNG(state.seed);const x=new Array(n).fill(0).map(()=>rand()*10-5);const y=x.map(v=>state.true.m*v+state.true.b+gaussian(rand)*state.true.sigma*3);state.data={x,y};fitScale();state.history=[];state.train.epoch=0;state.prevLine=null;state.phase=0;state.batchCache=null;updateStats(0,state.model.m,state.model.b);drawAll();updateEquation();}
    function fitScale(){const {x,y}=state.data;const pad=.2;const xmin=Math.min(...x),xmax=Math.max(...x);const ymin=Math.min(...y),ymax=Math.max(...y);const xr=xmax-xmin,yr=ymax-ymin;state.scale={xmin:xmin-xr*pad,xmax:xmax+xr*pad,ymin:ymin-yr*pad,ymax:ymax+yr*pad};}

    // ===== Model / Loss / Grads =====
    const predict=(x,m=state.model.m,b=state.model.b)=>m*x+b;
    function mse(xs,ys,m=state.model.m,b=state.model.b){const n=xs.length;let s=0;for(let i=0;i<n;i++){const e=(m*xs[i]+b)-ys[i];s+=e*e;}return s/n}
    function grads(xs,ys){const n=xs.length;let gM=0,gB=0;for(let i=0;i<n;i++){const e=(state.model.m*xs[i]+state.model.b)-ys[i];gM+=e*xs[i];gB+=e;}const c=2/n;return {dm:c*gM,db:c*gB}} 

    // ===== Classic step =====
    function stepClassic(){const {x,y}=state.data;const n=x.length;const batch=Math.max(1,Math.min(state.train.batch,n));const idx=[...Array(n).keys()];for(let i=n-1;i>0;i--){const j=Math.floor(Math.random()*(i+1));[idx[i],idx[j]]=[idx[j],idx[i]];}const xs=idx.slice(0,batch).map(i=>x[i]);const ys=idx.slice(0,batch).map(i=>y[i]);const {dm,db}=grads(xs,ys);state.model.m-=state.train.lr*dm;state.model.b-=state.train.lr*db;const loss=mse(x,y);state.history.push(loss);state.train.epoch+=1;updateStats(loss,state.model.m,state.model.b);drawAll();if(state.train.epoch>=state.train.epochs)state.train.running=false;}

    // ===== Phased step engine =====
    function prepareBatch(){const {x,y}=state.data;const n=x.length;const b=Math.max(1,Math.min(state.train.batch,n));const idx=[...Array(n).keys()];for(let i=n-1;i>0;i--){const j=Math.floor(Math.random()*(i+1));[idx[i],idx[j]]=[idx[j],idx[i]];}const xs=idx.slice(0,b).map(i=>x[i]);const ys=idx.slice(0,b).map(i=>y[i]);state.batchCache={xs,ys,yhat:null,e:null,sumE:null,sumEX:null,dm:null,db:null,dmVal:null,dbVal:null};}

    function nextPhase(){
      switch(state.phase){
        case 0: // evaluate yhat
          if(!state.batchCache) prepareBatch();
          const {xs,ys}=state.batchCache;const yhat=xs.map(x=>predict(x));state.batchCache.yhat=yhat;renderPhase(0,{xs,ys,yhat});state.phase=1;break;
        case 1: // errors & sums
          const e=state.batchCache.yhat.map((yh,i)=>yh-state.batchCache.ys[i]);
          const sumE=e.reduce((a,c)=>a+c,0);const sumEX=e.reduce((a,c,i)=>a+c*state.batchCache.xs[i],0);
          state.batchCache.e=e;state.batchCache.sumE=sumE;state.batchCache.sumEX=sumEX;renderPhase(1,{e,sumE,sumEX});state.phase=2;break;
        case 2: // grads & deltas
          const n=state.batchCache.xs.length;const dm=(2/n)*state.batchCache.sumEX;const db=(2/n)*state.batchCache.sumE;const dmVal=state.train.lr*dm;const dbVal=state.train.lr*db;Object.assign(state.batchCache,{dm,db,dmVal,dbVal});renderPhase(2,{n,dm,db,dmVal,dbVal,eta:state.train.lr});state.phase=3;break;
        case 3: // apply update
          state.prevLine={m:state.model.m,b:state.model.b};
          state.model.m=state.model.m-state.batchCache.dmVal;state.model.b=state.model.b-state.batchCache.dbVal;
          const fullLoss=mse(state.data.x,state.data.y);state.history.push(fullLoss);state.train.epoch+=1;updateStats(fullLoss,state.model.m,state.model.b);drawAll();renderPhase(3,{loss:fullLoss});state.phase=0;state.batchCache=null;break;
      }
      updatePhaseUI();
    }

    function renderPhase(p,info){const titles=['Phase 1 — Evaluate ŷ','Phase 2 — Errors & sums','Phase 3 — Gradients & deltas','Phase 4 — Apply update'];el('phaseTitle').textContent=titles[p];const mfmt=(n)=>Number.isFinite(n)?n.toFixed(6):'—';
      if(p===0){const {xs,ys,yhat}=info;el('calcMain').textContent=`For each: ŷᵢ = m·xᵢ + b\nCurrent m=${mfmt(state.model.m)}, b=${mfmt(state.model.b)}`;renderTable(xs.slice(0,50).map((x,i)=>({x,y:ys[i],yhat:yhat[i],e:NaN,ex:NaN})));}
      if(p===1){const {e,sumE,sumEX}=info;el('calcMain').textContent=`Errors eᵢ=ŷᵢ−yᵢ\nΣe=${mfmt(sumE)}\nΣ(e·x)=${mfmt(sumEX)}`;const {xs,ys,yhat}=state.batchCache;renderTable(xs.slice(0,50).map((x,i)=>({x,y:ys[i],yhat:yhat[i],e:e[i],ex:e[i]*x})));}
      if(p===2){const {n,dm,db,dmVal,dbVal,eta}=info;el('calcMain').textContent=`n=${n}\n∂MSE/∂m=(2/n)Σ(e·x)=${mfmt(dm)}\n∂MSE/∂b=(2/n)Σe=${mfmt(db)}\nη=${mfmt(eta)}\nΔm=η·∂MSE/∂m=${mfmt(dmVal)}\nΔb=η·∂MSE/∂b=${mfmt(dbVal)}`;}
      if(p===3){el('calcMain').textContent=`Apply update: m←m−Δm, b←b−Δb\nNew m=${mfmt(state.model.m)}, b=${mfmt(state.model.b)}\nFull MSE=${mfmt(info.loss)}`;}
    }

    function renderTable(rows){const header='i   x\t\t y\t\t ŷ\t\t e=ŷ−y\t e·x\n'+'-'.repeat(72)+'\n';const lines=rows.map((r,i)=>{const pad=v=> (Number.isFinite(v)?v.toFixed(6):'—').padStart(10,' ');return `${String(i).padStart(3,' ')} ${pad(r.x)} ${pad(r.y)} ${pad(r.yhat)} ${pad(r.e)} ${pad(r.ex)}`});el('tableBox').textContent=header+lines.join('\n')}

    function updatePhaseUI(){const titlesClassic='Classic mode';const titles=['Phase 1 — Evaluate ŷ','Phase 2 — Errors & sums','Phase 3 — Gradients & deltas','Phase 4 — Apply update'];const badge=el('phaseBadge');if(state.mode==='classic'){badge.textContent=titlesClassic;badge.className='badge phase-0';el('btnNextPhase').style.display='none';el('calcCard').style.display='none';}else{badge.textContent=titles[state.phase];badge.className='badge phase-'+state.phase;el('btnNextPhase').style.display='inline-block';el('calcCard').style.display='block';}updateEquation();drawAll();}

    // ===== Rendering (plots) =====
    const plot=el('plotCanvas');const loss=el('lossCanvas');const pctx=plot.getContext('2d');const lctx=loss.getContext('2d');
    function mapX(x){const {xmin,xmax}=state.scale;return (x-xmin)/(xmax-xmin)*(plot.width-40)+20}
    function mapY(y){const {ymin,ymax}=state.scale;return (1-(y-ymin)/(ymax-ymin))*(plot.height-40)+20}
    function drawGrid(ctx,w,h){ctx.save();ctx.strokeStyle='rgba(255,255,255,.06)';ctx.lineWidth=1;for(let x=40;x<w;x+=40){ctx.beginPath();ctx.moveTo(x,0);ctx.lineTo(x,h);ctx.stroke();}for(let y=40;y<h;y+=40){ctx.beginPath();ctx.moveTo(0,y);ctx.lineTo(w,y);ctx.stroke();}ctx.restore();}
    function clearCanvas(c){const ctx=c.getContext('2d');ctx.clearRect(0,0,c.width,c.height)}
    function resizeCanvases(){const dpr=window.devicePixelRatio||1;[plot,loss].forEach(c=>{const rect=c.getBoundingClientRect();c.width=Math.floor(rect.width*dpr);c.height=Math.floor(rect.height*dpr);c.getContext('2d').setTransform(dpr,0,0,dpr,0,0);});}
    function drawAll(){resizeCanvases();drawScatterAndLine();drawLoss();}
    function drawScatterAndLine(){clearCanvas(plot);const w=plot.width/(window.devicePixelRatio||1);const h=plot.height/(window.devicePixelRatio||1);drawGrid(pctx,w,h);pctx.strokeStyle='rgba(255,255,255,.25)';pctx.lineWidth=1.5;pctx.strokeRect(20,20,w-40,h-40);
      // data
      pctx.fillStyle='#60a5fa';for(let i=0;i<state.data.x.length;i++){const x=mapX(state.data.x[i]);const y=mapY(state.data.y[i]);pctx.beginPath();pctx.arc(x,y,3,0,Math.PI*2);pctx.fill();}
      // previous line (dashed)
      if(state.prevLine){const x0=state.scale.xmin,x1=state.scale.xmax;const y0=predict(x0,state.prevLine.m,state.prevLine.b),y1=predict(x1,state.prevLine.m,state.prevLine.b);pctx.strokeStyle='#a78bfa';pctx.lineWidth=1.5;pctx.setLineDash([6,6]);pctx.beginPath();pctx.moveTo(mapX(x0),mapY(y0));pctx.lineTo(mapX(x1),mapY(y1));pctx.stroke();pctx.setLineDash([]);} 
      // current line
      const x0=state.scale.xmin,x1=state.scale.xmax;const y0=predict(x0),y1=predict(x1);pctx.strokeStyle='#22c55e';pctx.lineWidth=2.5;pctx.beginPath();pctx.moveTo(mapX(x0),mapY(y0));pctx.lineTo(mapX(x1),mapY(y1));pctx.stroke();}
    function drawLoss(){clearCanvas(loss);const w=loss.width/(window.devicePixelRatio||1);const h=loss.height/(window.devicePixelRatio||1);drawGrid(lctx,w,h);lctx.strokeStyle='rgba(255,255,255,.25)';lctx.lineWidth=1.5;lctx.strokeRect(20,20,w-40,h-40);if(state.history.length===0)return;const padL=30,padR=20,padT=20,padB=30;const xmin=0,xmax=state.history.length-1;const ymin=Math.min(...state.history),ymax=Math.max(...state.history);const mapLX=(x)=>padL+(x-xmin)/Math.max(1,(xmax-xmin))*(w-padL-padR);const mapLY=(y)=>padT+(1-(y-ymin)/Math.max(1e-9,(ymax-ymin)))*(h-padT-padB);lctx.strokeStyle='#fbbf24';lctx.lineWidth=2;lctx.beginPath();lctx.moveTo(mapLX(0),mapLY(state.history[0]));for(let i=1;i<state.history.length;i++){lctx.lineTo(mapLX(i),mapLY(state.history[i]));}lctx.stroke();lctx.fillStyle='rgba(229,231,235,.9)';lctx.font='12px ui-sans-serif,system-ui';lctx.fillText('Epoch',w/2-18,h-8);lctx.save();lctx.translate(8,h/2+20);lctx.rotate(-Math.PI/2);lctx.fillText('Loss (MSE)',0,0);lctx.restore();}

    // ===== Stats & Equation =====
    function updateEquation(){const m=state.model.m.toFixed(6);const b=state.model.b.toFixed(6);document.getElementById('equation').textContent=`y = ${m}·x + ${b}`}
    function updateStats(lossVal,m,b){el('statEpoch').textContent=state.train.epoch.toString();el('statLoss').textContent=(isFinite(lossVal)?lossVal.toFixed(6):'—');el('statM').textContent=m.toFixed(4);el('statB').textContent=b.toFixed(4)}

    // ===== Mode switching =====
    function setMode(mode){state.mode=mode;state.phase=0;state.batchCache=null;updatePhaseUI();}
    el('modeClassic').addEventListener('click',()=>setMode('classic'));
    el('modePhased').addEventListener('click',()=>setMode('phased'));

    // ===== Buttons =====
    el('btnRegenerate').addEventListener('click',()=>{generateData(Math.round(parseFloat(el('points').value)));});
    el('btnShuffle').addEventListener('click',()=>{state.seed=Math.floor(Math.random()*1e9);generateData(Math.round(parseFloat(el('points').value)));});
    el('btnReset').addEventListener('click',()=>{state.model={m:0,b:0};state.history=[];state.train.epoch=0;state.prevLine=null;state.phase=0;state.batchCache=null;updateStats(0,state.model.m,state.model.b);drawAll();updatePhaseUI();document.getElementById('calcMain').textContent='—';document.getElementById('tableBox').textContent='—';});

    el('btnStart').addEventListener('click',(e)=>{state.train.running=!state.train.running;e.target.textContent=state.train.running?'Pause':'Start';if(state.train.running) trainLoop();});
    el('btnStep').addEventListener('click',()=>{ if(state.mode==='classic'){ stepClassic(); } else { nextPhase(); } });
    el('btnNextPhase').addEventListener('click',()=>{ nextPhase(); });

    function trainLoop(){ if(!state.train.running) return; if(state.mode==='classic'){ stepClassic(); } else { nextPhase(); } requestAnimationFrame(trainLoop); }

    // ===== Controls binding =====
    bindRangePair(el('points'), el('pointsNum'), v=>{state.train.batch=Math.min(state.train.batch,Math.round(v)); el('batch').max=v; el('batchNum').max=v;});
    bindRangePair(el('noise'), el('noiseNum'), v=>{state.true.sigma=v;});
    bindRangePair(el('trueM'), el('trueMNum'), v=>{state.true.m=v;});
    bindRangePair(el('trueB'), el('trueBNum'), v=>{state.true.b=v;});
    bindRangePair(el('lr'), el('lrNum'), v=>{state.train.lr=v;});
    bindRangePair(el('epochs'), el('epochsNum'), v=>{state.train.epochs=Math.round(v);});
    bindRangePair(el('batch'), el('batchNum'), v=>{state.train.batch=Math.max(1,Math.round(v));});

    // ===== Phase UI =====
    function updatePhaseUI(){const badge=el('phaseBadge');if(state.mode==='classic'){badge.textContent='Classic mode';badge.className='badge phase-0';el('btnNextPhase').style.display='none';el('calcCard').style.display='none';}else{const titles=['Phase 1 — Evaluate ŷ','Phase 2 — Errors & sums','Phase 3 — Gradients & deltas','Phase 4 — Apply update'];badge.textContent=titles[state.phase];badge.className='badge phase-'+state.phase;el('btnNextPhase').style.display='inline-block';el('calcCard').style.display='block';}}

    // ===== Init =====
    window.addEventListener('resize',()=>{drawAll();});
    generateData(120);
    updatePhaseUI();
    updateEquation();
  </script>
</body>
</html>
