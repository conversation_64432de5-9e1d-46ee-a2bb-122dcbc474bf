<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Decision Tree Training Visualizer — Multiclass • Multi‑Cluster • Resizable</title>
  <style>
    :root { --bg:#0f172a; --panel:#0b1220; --text:#e5e7eb; --muted:#9ca3af; --accent:#22c55e; --accent2:#60a5fa; --mag:#a78bfa; --danger:#f87171; --warning:#fbbf24; --left: 62%; }
    *{box-sizing:border-box}
    body{margin:0;font-family:ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto,Ubuntu;color:var(--text);background:radial-gradient(1000px 600px at 10% -10%,#1f2937,var(--bg))}
    .wrap{max-width:1280px;margin:24px auto;padding:16px}
    /* 2 panels + draggable splitter */
    .grid{display:grid;grid-template-columns:var(--left) 6px 1fr;gap:16px;align-items:start}
    .splitter{width:6px;height:100%;background:linear-gradient(180deg,rgba(255,255,255,.08),rgba(255,255,255,.02));border-radius:8px;cursor:col-resize;box-shadow:inset 0 0 0 1px rgba(255,255,255,.08)}
    .card{background:linear-gradient(180deg,rgba(255,255,255,.04),rgba(255,255,255,.02));border:1px solid rgba(255,255,255,.07);border-radius:16px;box-shadow:0 10px 30px rgba(0,0,0,.25)}
    .card h2{margin:0;padding:14px 16px;border-bottom:1px solid rgba(255,255,255,.06);font-size:18px;font-weight:600;color:#f8fafc}
    .card .body{padding:16px}

    .canvases{display:grid;gap:12px}
    canvas{width:100%;height:420px;background:#0a1120;border:1px solid rgba(255,255,255,.06);border-radius:16px}
    #metricCanvas{height:180px}

    .legend{display:flex;gap:12px;font-size:13px;color:var(--muted);margin-top:8px;flex-wrap:wrap}
    .dot{display:inline-block;width:10px;height:10px;border-radius:50%;margin-right:6px}

    .equation{margin-top:10px;font-family:ui-monospace,Consolas,Menlo,monospace;color:#c7d2fe;background:var(--panel);border:1px solid rgba(255,255,255,.08);padding:10px 12px;border-radius:10px}

    .controls{display:grid;grid-template-columns:1fr 1fr;gap:12px}
    .row{display:grid;grid-template-columns:170px 1fr 110px;gap:10px;align-items:center}
    label{color:var(--muted);font-size:12px}
    select,input[type=number],input[type=range]{width:100%;background:#0b1220;color:#e5e7eb;border:1px solid rgba(255,255,255,.08);border-radius:10px;padding:8px 10px}
    input[type=range]{appearance:none;height:6px;border-radius:999px;background:#222a3b}
    input[type=range]::-webkit-slider-thumb{appearance:none;width:16px;height:16px;border-radius:999px;background:var(--accent2);border:2px solid white;box-shadow:0 1px 4px rgba(0,0,0,.4)}
    .btns{display:flex;gap:10px;flex-wrap:wrap;margin-top:8px}
    button{border:1px solid rgba(255,255,255,.12);color:#e5e7eb;background:#101626;padding:10px 14px;border-radius:12px;cursor:pointer;font-weight:600;letter-spacing:.2px;transition:transform .06s ease,background .2s ease,border-color .2s ease}
    button:hover{transform:translateY(-1px);border-color:rgba(255,255,255,.22)}
    .primary{background:linear-gradient(135deg,#1d4ed8,#22c55e);border:none}
    .danger{background:linear-gradient(135deg,#991b1b,#ef4444);border:none}
    .warn{background:linear-gradient(135deg,#92400e,#f59e0b);border:none}
    .muted{background:#0b1220}

    .status{display:grid;grid-template-columns:repeat(3,1fr);gap:10px;margin-top:12px}
    .stat{background:#0b1220;border:1px solid rgba(255,255,255,.08);border-radius:12px;padding:12px}
    .stat .k{color:var(--muted);font-size:12px}
    .stat .v{font-size:18px;font-weight:700}

    .mode{display:flex;gap:8px;align-items:center;margin:8px 0 10px}
    .badge{display:inline-block;padding:4px 8px;border-radius:999px;font-size:12px;border:1px solid rgba(255,255,255,.15)}
    .phase-0{background:#0b3b1d;border-color:#1f7a3e}
    .phase-1{background:#38210b;border-color:#8a5a10}
    .phase-2{background:#2a223f;border-color:#6b5ca3}
    .phase-3{background:#401d2b;border-color:#b0476c}

    .calc-grid{display:grid;grid-template-columns:1fr;gap:10px}
    .calc-box{background:#0b1220;border:1px solid rgba(255,255,255,.1);border-radius:12px;padding:10px}
    .calc-box h3{margin:0 0 6px 0;font-size:14px;color:#dbeafe}
    .mono{font-family:ui-monospace,Consolas,Menlo,monospace;white-space:pre-wrap}
    .scroll{max-height:260px;overflow:auto}

    /* Big, scrollable tree area in the Controls card */
    .treePanel{margin-top:14px}
    .treePanel h3{margin:0 0 8px 0;font-size:14px;color:#dbeafe}
    .treeWrap{background:#0a1120;border:1px solid rgba(255,255,255,.08);border-radius:12px;overflow:auto;max-height:560px}
    #treeCanvas{display:block;min-width:1400px;min-height:820px}

    @media (max-width:980px){.grid{grid-template-columns:1fr 6px 1fr}}
  </style>
</head>
<body>
  <div class="wrap">
    <div class="grid" id="gridRoot">
      <!-- Viz -->
      <div class="card">
        <h2>Decision Tree — Training Playground <span id="phaseBadge" class="badge phase-0" style="margin-left:8px">Classic mode</span></h2>
        <div class="body">
          <div class="canvases">
            <canvas id="plotCanvas"></canvas>
            <canvas id="metricCanvas"></canvas>
          </div>
          <div class="legend" id="legend"></div>
          <div class="equation" id="equation">criterion = Gini: G = 1 − Σ p²,  Gain = Imp(P) − (nL/n)·Imp(L) − (nR/n)·Imp(R)</div>
        </div>
      </div>

      <!-- Draggable splitter -->
      <div class="splitter" id="splitter" title="Drag to resize panels"></div>

      <!-- Controls (with BIG tree) -->
      <div class="card">
        <h2>Controls</h2>
        <div class="body">
          <div class="mode">
            <label style="color:var(--muted);font-size:12px">Mode:</label>
            <button id="modeClassic" class="muted">Classic</button>
            <button id="modePhased" class="muted">Multi‑phase</button>
          </div>
          <div class="controls">
            <div class="row"><label># Points</label><input id="points" type="range" min="100" max="10000" value="1200" step="50"/><input id="pointsNum" type="number" min="100" max="10000" value="1200"/></div>
            <div class="row"><label>Noise σ</label><input id="noise" type="range" min="0" max="3" value="0.6" step="0.01"/><input id="noiseNum" type="number" min="0" max="3" value="0.6" step="0.01"/></div>
            <div class="row"><label>Separation Δ</label><input id="sep" type="range" min="0" max="8" value="3.5" step="0.1"/><input id="sepNum" type="number" min="0" max="8" value="3.5" step="0.1"/></div>
            <div class="row"><label>Classes (K)</label><input id="classes" type="range" min="2" max="8" value="3" step="1"/><input id="classesNum" type="number" min="2" max="8" value="3" step="1"/></div>
            <div class="row"><label>Clusters / class</label><input id="clusters" type="range" min="1" max="6" value="2" step="1"/><input id="clustersNum" type="number" min="1" max="6" value="2" step="1"/></div>
            <div class="row"><label>Layout (viz %)</label><input id="layout" type="range" min="40" max="80" value="62" step="1"/><input id="layoutNum" type="number" min="40" max="80" value="62" step="1"/></div>
            <div class="row"><label>Max depth</label><input id="maxDepth" type="range" min="1" max="14" value="6" step="1"/><input id="maxDepthNum" type="number" min="1" max="14" value="6" step="1"/></div>
            <div class="row"><label>Min samples/leaf</label><input id="minLeaf" type="range" min="1" max="500" value="30" step="1"/><input id="minLeafNum" type="number" min="1" max="500" value="30" step="1"/></div>
            <div class="row"><label>Criterion</label>
              <select id="criterion">
                <option value="gini" selected>Gini</option>
                <option value="entropy">Entropy</option>
              </select>
            </div>
            <div class="row"><label>Max thresholds/feature</label><input id="kThresh" type="range" min="5" max="160" value="60" step="1"/><input id="kThreshNum" type="number" min="5" max="160" value="60" step="1"/></div>
          </div>
          <div class="btns">
            <button class="primary" id="btnStart">Start</button>
            <button class="muted" id="btnStep">Step</button>
            <button class="muted" id="btnNextPhase" style="display:none">Next Phase</button>
            <button class="warn" id="btnShuffle">Shuffle Data</button>
            <button class="danger" id="btnReset">Reset</button>
            <button class="muted" id="btnRegenerate">Regenerate Dataset</button>
          </div>
          <div class="status">
            <div class="stat"><div class="k">Nodes</div><div class="v" id="statNodes">0</div></div>
            <div class="stat"><div class="k">Depth</div><div class="v" id="statDepth">0</div></div>
            <div class="stat"><div class="k">Accuracy</div><div class="v" id="statAcc">—</div></div>
          </div>

          <div class="treePanel">
            <h3>Tree (scrollable)</h3>
            <div class="treeWrap">
              <canvas id="treeCanvas"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Calculations -->
    <div id="calcCard" class="card" style="margin-top:16px; display:none">
      <h2>Live Calculations</h2>
      <div class="body calc-grid">
        <div class="calc-box"><h3 id="phaseTitle">Phase 1 — Evaluate candidate splits</h3><div class="mono" id="calcMain">—</div></div>
        <div class="calc-box"><h3>Candidate table (top 80 by gain)</h3><div class="mono scroll" id="tableBox">—</div></div>
      </div>
    </div>

    <!-- Tests -->
    <div id="testCard" class="card" style="margin-top:16px;">
      <h2>Self‑Tests</h2>
      <div class="body">
        <div class="btns">
          <button id="btnRunTests" class="primary">Run Tests</button>
        </div>
        <div id="testResults" class="mono" style="margin-top:10px;white-space:pre-wrap"></div>
      </div>
    </div>
  </div>

  <script>
    // ===== Helpers =====
    const el=(id)=>document.getElementById(id);
    const RNG=(seed=1234567)=>{let s=seed>>>0;return()=>{s=(s*1664525+1013904223)>>>0;return s/0xffffffff;}};
    const bindPair=(a,b,fn)=>{const sync=(src,dst)=>{dst.value=src.value;fn(parseFloat(src.value));};a.addEventListener('input',()=>sync(a,b));b.addEventListener('input',()=>sync(b,a));fn(parseFloat(a.value));};
    const COLORS=["#60a5fa","#f472b6","#34d399","#fbbf24","#a78bfa","#f87171","#22d3ee","#d946ef"];

    // ===== State =====
    const state={
      seed:42,
      K:3,
      C:2, // clusters per class
      data:{x1:[],x2:[],y:[]},
      gen:{sigma:0.6,sep:3.5},
      tree:{maxDepth:6,minLeaf:30,criterion:'gini',kThresh:60},
      model:{nodes:[],root:null},
      queue:[],
      training:{running:false,phase:0},
      metrics:{history:[],acc:NaN},
      scale:{x1min:-5,x1max:5,x2min:-5,x2max:5},
      current:null,
      batchCache:null
    };

    // ===== Draggable splitter + layout slider =====
    const gridRoot=document.getElementById('gridRoot');
    const splitter=document.getElementById('splitter');
    function setLeftPct(pct){ pct=Math.min(80,Math.max(40,Math.round(pct))); gridRoot.style.setProperty('--left', pct+'%'); el('layout').value=String(pct); el('layoutNum').value=String(pct); requestAnimationFrame(()=>{ drawAll(); }); }
    let dragging=false;
    splitter.addEventListener('mousedown',(e)=>{ dragging=true; document.body.style.userSelect='none'; });
    window.addEventListener('mousemove',(e)=>{ if(!dragging) return; const rect=gridRoot.getBoundingClientRect(); const dx=e.clientX-rect.left; const pct=dx/rect.width*100; setLeftPct(pct); });
    window.addEventListener('mouseup',()=>{ dragging=false; document.body.style.userSelect=''; });

    // ===== Data generation: K classes, C clusters per class (mixture of Gaussians) =====
    function gaussian(rand){let u=0,v=0;while(u===0)u=rand();while(v===0)v=rand();return Math.sqrt(-2*Math.log(u))*Math.cos(2*Math.PI*v)}
    function clusterCenters(K,C,sep){ const centers=[]; for(let k=0;k<K;k++){ const ang0=2*Math.PI*k/K; for(let c=0;c<C;c++){ const r=sep*(0.5+0.4*c); const jitter= (c%2? 0.0: (k%2? 0.15:-0.15)); const ang=ang0 + (2*Math.PI*c/(K*C)) + jitter; const cx=r*Math.cos(ang); const cy=r*Math.sin(ang); centers.push({k,c,x:cx,y:cy}); } } return centers; }
    function generateData(N=1200){ const rand=RNG(state.seed); const {sigma,sep}=state.gen; const K=state.K, C=state.C; const centers=clusterCenters(K,C,sep); const x1=new Array(N), x2=new Array(N), y=new Array(N);
      for(let i=0;i<N;i++){
        const cls=Math.floor(rand()*K); // pick class
        const choices=centers.filter(o=>o.k===cls);
        const cc=choices[Math.floor(rand()*choices.length)];
        x1[i]=cc.x + gaussian(rand)*sigma + (rand()-0.5)*0.25;
        x2[i]=cc.y + gaussian(rand)*sigma + (rand()-0.5)*0.25;
        y[i]=cls;
      }
      state.data={x1,x2,y}; fitScale(); resetTree(); buildLegend(); drawAll(); }

    function fitScale(){ const {x1,x2}=state.data; const pad=.2; const x1min=Math.min(...x1),x1max=Math.max(...x1); const x2min=Math.min(...x2),x2max=Math.max(...x2); const xr=x1max-x1min,yr=x2max-x2min; state.scale={x1min:x1min-xr*pad,x1max:x1max+xr*pad,x2min:x2min-yr*pad,x2max:x2max+yr*pad}; }

    // ===== Impurity (multiclass) =====
    function giniCounts(counts){ const n=counts.reduce((a,b)=>a+b,0); if(n===0) return 0; let s=0; for(const c of counts){ const p=c/n; s+=p*p; } return 1-s; }
    function entropyCounts(counts){ const n=counts.reduce((a,b)=>a+b,0); if(n===0) return 0; let h=0; for(const c of counts){ if(c>0){ const p=c/n; h+= -p*Math.log2(p); } } return h; }
    function impurityCounts(counts){ return state.tree.criterion==='gini'? giniCounts(counts):entropyCounts(counts); }

    // ===== Tree structures =====
    function makeLeaf(idx){ const counts=Array(state.K).fill(0); for(const i of idx){ counts[state.data.y[i]]++; } const pred=counts.indexOf(Math.max(...counts)); return {leaf:true,idx,rect:null,counts,pred, depth:0, parent:null}; }
    function resetTree(){ const idx=[...Array(state.data.y.length).keys()]; const root=makeLeaf(idx); root.rect=fullRect(); state.model={nodes:[root],root}; state.queue=[root]; state.current=root; state.training.phase=0; state.metrics.history=[]; updateStats(); }
    function fullRect(){ const {x1min,x1max,x2min,x2max}=state.scale; return {x1min,x1max,x2min,x2max}; }

    // ===== Split search =====
    function uniqueSorted(values){ return Array.from(new Set(values)).sort((a,b)=>a-b); }
    function candidateThresholds(vals,k){ const uniq=uniqueSorted(vals); if(uniq.length<=1) return []; const mids=[]; for(let i=0;i<uniq.length-1;i++){ const a=uniq[i], b=uniq[i+1]; mids.push((a+b)/2); } if(mids.length<=k) return mids; const step=(mids.length-1)/(k-1); return Array.from({length:k},(_,i)=>mids[Math.round(i*step)]); }

    function evaluateBestSplit(node){ const idx=node.idx; const n=idx.length; if(n<2) return null; const parentCounts=node.counts.slice(); const impP=impurityCounts(parentCounts); let best=null; const rows=[]; const feats=[["x1",state.data.x1],["x2",state.data.x2]];
      for(const [feat,arr] of feats){ const values=idx.map(i=>arr[i]); const ths=candidateThresholds(values,state.tree.kThresh); for(const thr of ths){ const L=Array(state.K).fill(0), R=Array(state.K).fill(0); for(const i of idx){ const c=state.data.y[i]; if(arr[i]<=thr) L[c]++; else R[c]++; } const nL=L.reduce((a,b)=>a+b,0), nR=R.reduce((a,b)=>a+b,0); if(nL<state.tree.minLeaf || nR<state.tree.minLeaf) continue; const impL=impurityCounts(L), impR=impurityCounts(R); const gain=impP - (nL/n)*impL - (nR/n)*impR; rows.push({feat,thr,nL,nR,impP,impL,impR,gain}); if(!best || gain>best.gain){ best={feat,thr,gain,nL,nR,L,R,impP,impL,impR}; } } }
      if(!best) return null; const arr = best.feat==='x1'? state.data.x1 : state.data.x2; const leftIdx=[], rightIdx=[]; for(const i of idx){ (arr[i] <= best.thr? leftIdx : rightIdx).push(i); } return { ...best, leftIdx, rightIdx, rows: rows.sort((a,b)=>b.gain-a.gain).slice(0,300) } }

    // ===== Training steps =====
    function nonTrivial(counts){ return counts.filter(c=>c>0).length>1; }
    function canSplit(node){ if(node.leaf===false) return false; if(node.idx.length < 2*state.tree.minLeaf) return false; if(node.depth >= state.tree.maxDepth) return false; return nonTrivial(node.counts); }
    function nextSplittableNode(){ while(state.queue.length){ const n=state.queue.shift(); if(canSplit(n)) return n; } return null; }
    function classicStep(){ if(state.queue.length===0) return finishEpoch(); const node=nextSplittableNode(); if(!node){ return finishEpoch(); } const best=evaluateBestSplit(node); if(!best){ node.leaf=true; return; } applySplit(node,best); updateStats(); drawAll(); }
    function finishEpoch(){ const acc=accuracy(); state.metrics.history.push(acc); updateStats(); }

    function applySplit(node,best){ node.leaf=false; node.feat=best.feat; node.thr=best.thr; node.gain=best.gain; const left=makeLeaf(best.leftIdx), right=makeLeaf(best.rightIdx); left.depth=node.depth+1; right.depth=node.depth+1; left.parent=node; right.parent=node; const r=node.rect; if(best.feat==='x1'){ left.rect={x1min:r.x1min,x1max:best.thr,x2min:r.x2min,x2max:r.x2max}; right.rect={x1min:best.thr,x1max:r.x1max,x2min:r.x2min,x2max:r.x2max}; } else { left.rect={x1min:r.x1min,x1max:r.x1max,x2min:r.x2min,x2max:best.thr}; right.rect={x1min:r.x1min,x1max:r.x1max,x2min:best.thr,x2max:r.x2max}; } node.left=left; node.right=right; state.model.nodes.push(left,right); state.queue.push(left,right); state.current=left; }

    // ===== Phased engine =====
    function nextPhase(){ switch(state.training.phase){ case 0: { const node = state.current && canSplit(state.current)? state.current : nextSplittableNode(); if(!node){ finishEpoch(); break; } state.current=node; const best=evaluateBestSplit(node); state.batchCache={node,best}; renderPhase(0,{node,best}); state.training.phase=1; break; } case 1: { if(!state.batchCache || !state.batchCache.best){ state.training.phase=0; break; } renderPhase(1,{best:state.batchCache.best}); state.training.phase=2; break; } case 2: { if(!state.batchCache || !state.batchCache.best){ state.training.phase=0; break; } applySplit(state.batchCache.node,state.batchCache.best); renderPhase(2,{applied:true}); state.training.phase=3; break; } case 3: { const acc=accuracy(); state.metrics.history.push(acc); updateStats(); drawAll(); renderPhase(3,{acc}); state.batchCache=null; state.training.phase=0; break; } } updatePhaseUI(); }

    // ===== Prediction & Metrics =====
    function predictOne(i){ let n=state.model.root; while(n && n.leaf===false){ const featVal = (n.feat==='x1'? state.data.x1[i] : state.data.x2[i]); n = (featVal <= n.thr)? n.left : n.right; } return n?n.pred:0; }
    function accuracy(){ const N=state.data.y.length; let ok=0; for(let i=0;i<N;i++){ if(predictOne(i)===state.data.y[i]) ok++; } return ok/N; }

    // ===== Legend =====
    function buildLegend(){ const L=el('legend'); L.innerHTML=''; for(let k=0;k<state.K;k++){ const span=document.createElement('span'); const dot=document.createElement('span'); dot.className='dot'; dot.style.background=COLORS[k%COLORS.length]; span.appendChild(dot); span.appendChild(document.createTextNode('Class '+k)); L.appendChild(span); } }

    // ===== Rendering (plots) =====
    const plot=el('plotCanvas'); const metr=el('metricCanvas'); const tree=el('treeCanvas'); const pctx=plot.getContext('2d'); const mctx=metr.getContext('2d'); const tctx=tree.getContext('2d');
    function mapX1(x){const {x1min,x1max}=state.scale; return (x-x1min)/(x1max-x1min)*(plot.width-40)+20}
    function mapX2(y){const {x2min,x2max}=state.scale; return (1-(y-x2min)/(x2max-x2min))*(plot.height-40)+20}
    function clearCanvas(c){const ctx=c.getContext('2d'); ctx.clearRect(0,0,c.width,c.height);}
    function drawGrid(ctx,w,h){ctx.save();ctx.strokeStyle='rgba(255,255,255,.06)';ctx.lineWidth=1;for(let x=40;x<w;x+=40){ctx.beginPath();ctx.moveTo(x,0);ctx.lineTo(x,h);ctx.stroke();}for(let y=40;y<h;y+=40){ctx.beginPath();ctx.moveTo(0,y);ctx.lineTo(w,y);ctx.stroke();}ctx.restore();}
    function resizeCanvases(){const dpr=window.devicePixelRatio||1; [plot,metr].forEach(c=>{const rect=c.getBoundingClientRect(); c.width=Math.floor(rect.width*dpr); c.height=Math.floor(rect.height*dpr); c.getContext('2d').setTransform(dpr,0,0,dpr,0,0);}); resizeTreeCanvas(); }

    function drawAll(){ resizeCanvases(); drawSurface(); drawPoints(); drawSplits(); drawMetrics(); drawTreeCanvas(); updateEquation(); }

    function drawSurface(){ const w=plot.width/(window.devicePixelRatio||1), h=plot.height/(window.devicePixelRatio||1); clearCanvas(plot); drawGrid(pctx,w,h); pctx.strokeStyle='rgba(255,255,255,.25)'; pctx.lineWidth=1.5; pctx.strokeRect(20,20,w-40,h-40); const cols=140, rows=100; const {x1min,x1max,x2min,x2max}=state.scale; const dx=(x1max-x1min)/cols, dy=(x2max-x2min)/rows; for(let r=0;r<rows;r++){ for(let c=0;c<cols;c++){ const x1=x1min + (c+0.5)*dx; const x2=x2min + (r+0.5)*dy; let n=state.model.root; while(n && n.leaf===false){ const f = (n.feat==='x1'? x1 : x2); n = (f <= n.thr)? n.left:n.right; } const cls=n?n.pred:0; pctx.fillStyle = hexToRgba(COLORS[cls%COLORS.length],0.12); pctx.fillRect(mapX1(x1)-0.5, mapX2(x2)-0.5, 2, 2); }} }
    function drawPoints(){ const N=state.data.y.length; for(let i=0;i<N;i++){ const xi1=state.data.x1[i], xi2=state.data.x2[i], yi=state.data.y[i]; pctx.beginPath(); pctx.arc(mapX1(xi1), mapX2(xi2), 2.4, 0, Math.PI*2); pctx.fillStyle = COLORS[yi%COLORS.length]; pctx.fill(); } }
    function drawSplits(){ pctx.save(); function drawNode(n){ if(!n) return; if(n===state.current){ pctx.fillStyle='rgba(251,191,36,0.10)'; const r=n.rect; pctx.fillRect(mapX1(r.x1min), mapX2(r.x2max), mapX1(r.x1max)-mapX1(r.x1min), mapX2(r.x2min)-mapX2(r.x2max)); }
        if(n.leaf===false){ const r=n.rect; pctx.strokeStyle='#22c55e'; pctx.lineWidth=2; if(n.feat==='x1'){ const x=mapX1(n.thr); pctx.beginPath(); pctx.moveTo(x,mapX2(r.x2max)); pctx.lineTo(x,mapX2(r.x2min)); pctx.stroke(); } else { const y=mapX2(n.thr); pctx.beginPath(); pctx.moveTo(mapX1(r.x1min),y); pctx.lineTo(mapX1(r.x1max),y); pctx.stroke(); } drawNode(n.left); drawNode(n.right); } }
      drawNode(state.model.root); pctx.restore(); }

    function drawMetrics(){ clearCanvas(metr); const w=metr.width/(window.devicePixelRatio||1), h=metr.height/(window.devicePixelRatio||1); drawGrid(mctx,w,h); mctx.strokeStyle='rgba(255,255,255,.25)'; mctx.lineWidth=1.5; mctx.strokeRect(20,20,w-40,h-40); if(state.metrics.history.length===0) return; const padL=30,padR=20,padT=20,padB=30; const xmin=0,xmax=state.metrics.history.length-1; const ymin=Math.min(...state.metrics.history),ymax=1; const mapLX=(x)=>padL+(x-xmin)/Math.max(1,(xmax-xmin))*(w-padL-padR); const mapLY=(y)=>padT+(1-(y-ymin)/Math.max(1e-9,(ymax-ymin)))*(h-padT-padB); mctx.strokeStyle='#22c55e'; mctx.lineWidth=2; mctx.beginPath(); mctx.moveTo(mapLX(0),mapLY(state.metrics.history[0])); for(let i=1;i<state.metrics.history.length;i++){ mctx.lineTo(mapLX(i),mapLY(state.metrics.history[i])); } mctx.stroke(); mctx.fillStyle='rgba(229,231,235,.9)'; mctx.font='12px ui-sans-serif,system-ui'; mctx.fillText('Step',w/2-14,h-8); mctx.save(); mctx.translate(8,h/2+20); mctx.rotate(-Math.PI/2); mctx.fillText('Accuracy',0,0); mctx.restore(); }

    // ===== Big scrollable tree canvas =====
    function resizeTreeCanvas(){ const wrap=document.querySelector('.treeWrap'); const dpr=window.devicePixelRatio||1; const {width,height}=computeTreeSize(); const W=Math.max(width, wrap.clientWidth); const H=Math.max(height, wrap.clientHeight); tree.width=Math.floor(W*dpr); tree.height=Math.floor(H*dpr); tctx.setTransform(dpr,0,0,dpr,0,0); }

    function computeTreeStats(root){ let depth=0, leaves=0; (function walk(n){ if(!n) return; depth=Math.max(depth,n.depth||0); if(n.leaf) leaves++; else { walk(n.left); walk(n.right); } })(root); return {depth,leaves:Math.max(1,leaves)}; }

    function computeTreeSize(){ const {depth,leaves}=computeTreeStats(state.model.root||{}); const padX=80, padY=50; const colW=220; const rowH=110; const width = padX*2 + (Math.max(1,leaves)-1)*colW; const height = padY*2 + (Math.max(0,depth))*rowH + 80; return {width,height,padX,padY,colW,rowH}; }

    function layoutTree(root){ const stats=computeTreeSize(); const leaves=[]; (function collect(n){ if(!n) return; if(n.leaf) leaves.push(n); collect(n.left); collect(n.right); })(root); let x=stats.padX; const step = leaves.length>1? (stats.width-2*stats.padX)/(leaves.length-1) : 0; (function setPos(n){ if(!n) return; if(n.leaf){ n._tx=x; x+=step; } else { setPos(n.left); setPos(n.right); const lx=n.left? n.left._tx: (n.right?n.right._tx:x); const rx=n.right? n.right._tx: (n.left?n.left._tx:x); n._tx=(lx+rx)/2; } })(root); (function setY(n){ if(!n) return; n._ty = stats.padY + (n.depth||0)*stats.rowH; setY(n.left); setY(n.right); })(root); return stats; }

    function drawTreeCanvas(){ const W=tree.width/(window.devicePixelRatio||1), H=tree.height/(window.devicePixelRatio||1); clearCanvas(tree); tctx.fillStyle='#0a1120'; tctx.fillRect(0,0,W,H); tctx.strokeStyle='rgba(255,255,255,.25)'; tctx.strokeRect(0,0,W,H); const root=state.model.root; if(!root) return; layoutTree(root);
      tctx.lineWidth=1.5; function edge(a,b,label){ tctx.strokeStyle='rgba(255,255,255,.25)'; tctx.beginPath(); tctx.moveTo(a._tx,a._ty+16); tctx.lineTo(b._tx,b._ty-16); tctx.stroke(); if(label){ drawTreeEdgeLabel(label,(a._tx+b._tx)/2,(a._ty+b._ty)/2); } }
      (function walkEdges(n){ if(!n || n.leaf) return; edge(n,n.left,`≤ ${n.thr.toFixed(3)}`); edge(n,n.right,`> ${n.thr.toFixed(3)}`); walkEdges(n.left); walkEdges(n.right); })(root);
      function nodeBox(n){ const top = n.leaf? `leaf • pred=${n.pred}` : `${n.feat} ≤ ${n.thr.toFixed(3)}`; const countsTxt='['+n.counts.map((c,i)=>`${i}:${c}`).join(' ')+']'; const bot = `n=${n.idx.length} | ${countsTxt}`; const w = Math.max(170, 8 * Math.max(top.length, bot.length)); const h = 50; const x=n._tx - w/2, y=n._ty - h/2; tctx.fillStyle = n===state.current? 'rgba(251,191,36,0.12)' : 'rgba(255,255,255,0.04)'; tctx.strokeStyle='rgba(255,255,255,0.18)'; tctx.lineWidth=1; tctx.beginPath(); if(tctx.roundRect){ tctx.roundRect(x,y,w,h,10);} else { tctx.rect(x,y,w,h);} tctx.fill(); tctx.stroke(); tctx.fillStyle='#e5e7eb'; tctx.font='12px ui-monospace,Consolas,Menlo,monospace'; tctx.textBaseline='middle'; tctx.textAlign='center'; tctx.fillText(top, n._tx, n._ty-10); tctx.fillStyle='var(--muted)'; tctx.fillText(bot, n._tx, n._ty+10); }
      (function walkNodes(n){ if(!n) return; nodeBox(n); walkNodes(n.left); walkNodes(n.right); })(root);
    }

    function drawTreeEdgeLabel(txt,x,y){ tctx.save(); tctx.font='11px ui-monospace,Consolas,Menlo,monospace'; const pad=4; const w=tctx.measureText(txt).width+pad*2; const h=16; tctx.fillStyle='rgba(15,23,42,0.85)'; tctx.strokeStyle='rgba(255,255,255,0.25)'; tctx.beginPath(); if(tctx.roundRect){ tctx.roundRect(x-w/2,y-h/2,w,h,6);} else { tctx.rect(x-w/2,y-h/2,w,h);} tctx.fill(); tctx.stroke(); tctx.fillStyle='#e5e7eb'; tctx.textAlign='center'; tctx.textBaseline='middle'; tctx.fillText(txt,x,y+0.5); tctx.restore(); }

    // ===== Equation / Stats =====
    function updateEquation(){ const crit=state.tree.criterion==='gini'? 'Gini: G=1−Σp²' : 'Entropy: H=−Σ p log₂ p'; el('equation').textContent=`criterion = ${crit},  Gain = Imp(P) − (nL/n)·Imp(L) − (nR/n)·Imp(R)`; }
    function updateStats(){ const nodes=state.model.nodes.length; let depth=0; (function walk(n){ if(!n) return; depth=Math.max(depth,n.depth||0); if(n.leaf===false){ walk(n.left); walk(n.right);} })(state.model.root); const acc=accuracy(); state.metrics.acc=acc; el('statNodes').textContent=String(nodes); el('statDepth').textContent=String(depth); el('statAcc').textContent=(isFinite(acc)?acc.toFixed(3):'—'); }

    // ===== Calculations UI =====
    function renderTable(rows){
      const header='feat\t thr\t nL\t nR\t Imp(P)\t Imp(L)\t Imp(R)\t Gain\n' + '-'.repeat(100) + '\n';
      const pad=(v)=> (Number.isFinite(v)?v.toFixed(6):'—').padStart(8,' ');
      const lines=rows.slice(0,80).map(r=>`${r.feat.padEnd(3,' ')}\t${pad(r.thr)}\t${String(r.nL).padStart(4)}\t${String(r.nR).padStart(4)}\t${pad(r.impP)}\t${pad(r.impL)}\t${pad(r.impR)}\t${pad(r.gain)}`);
      el('tableBox').textContent=header+lines.join('\n');
    }
    function renderPhase(p,info){ const titles=['Phase 1 — Evaluate candidate splits','Phase 2 — Best split calculations','Phase 3 — Apply split','Phase 4 — Update metrics']; el('phaseTitle').textContent=titles[p]; const f=(x)=>Number.isFinite(x)?x.toFixed(6):'—'; if(p===0){ const {node,best}=info; if(!best){ el('calcMain').textContent='No valid split (minLeaf/depth/purity reached).'; el('tableBox').textContent='—'; return; } const head=`Node depth=${node.depth}, size=${node.idx.length}\nParent Impurity = ${f(best.impP)}\nCandidates (top by gain):`; el('calcMain').textContent=head; renderTable(best.rows); } if(p===1){ const {best}=info; if(!best){ el('calcMain').textContent='—'; return; } el('calcMain').textContent=`Best: feat=${best.feat}, thr=${f(best.thr)}\nImp(P)=${f(best.impP)}\nImp(L)=${f(best.impL)} with nL=${best.nL}\nImp(R)=${f(best.impR)} with nR=${best.nR}\nGain = Imp(P) − (nL/n)·Imp(L) − (nR/n)·Imp(R) = ${f(best.gain)}`; } if(p===2){ el('calcMain').textContent='Applied split: new left/right children added to queue.'; } if(p===3){ el('calcMain').textContent=`Accuracy snapshot = ${f(info.acc)}`; } }

    function updatePhaseUI(){ const badge=el('phaseBadge'); if(!state.training.running && el('modeClassic').classList.contains('active')){ badge.textContent='Classic mode'; badge.className='badge phase-0'; el('btnNextPhase').style.display='none'; el('calcCard').style.display='none'; } else if(el('modePhased').classList.contains('active')){ const titles=['Phase 1 — Evaluate candidate splits','Phase 2 — Best split calculations','Phase 3 — Apply split','Phase 4 — Update metrics']; badge.textContent=titles[state.training.phase]; badge.className='badge phase-'+state.training.phase; el('btnNextPhase').style.display='inline-block'; el('calcCard').style.display='block'; } else { badge.textContent='Classic mode'; badge.className='badge phase-0'; el('btnNextPhase').style.display='none'; el('calcCard').style.display='none'; } updateEquation(); drawAll(); }

    // ===== Events =====
    function setMode(mode){ el('modeClassic').classList.toggle('active', mode==='classic'); el('modePhased').classList.toggle('active', mode==='phased'); state.training.phase=0; state.training.running=false; updatePhaseUI(); }
    el('modeClassic').addEventListener('click',()=>setMode('classic'));
    el('modePhased').addEventListener('click',()=>setMode('phased'));

    el('btnRegenerate').addEventListener('click',()=>{ generateData(Math.round(parseFloat(el('points').value))); });
    el('btnShuffle').addEventListener('click',()=>{ state.seed=Math.floor(Math.random()*1e9); generateData(Math.round(parseFloat(el('points').value))); });
    el('btnReset').addEventListener('click',()=>{ resetTree(); drawAll(); el('calcMain').textContent='—'; el('tableBox').textContent='—'; state.metrics.history=[]; });

    el('btnStart').addEventListener('click',(e)=>{ const phased = el('modePhased').classList.contains('active'); state.training.running=!state.training.running; e.target.textContent=state.training.running?'Pause':'Start'; if(state.training.running){ (function loop(){ if(!state.training.running) return; if(phased){ nextPhase(); } else { classicStep(); } requestAnimationFrame(loop); })(); }});
    el('btnStep').addEventListener('click',()=>{ const phased = el('modePhased').classList.contains('active'); if(!state.training.running){ if(phased){ nextPhase(); } else { classicStep(); } }});
    el('btnNextPhase').addEventListener('click',()=>{ if(!state.training.running) nextPhase(); });

    // ===== Bind controls =====
    bindPair(el('points'),el('pointsNum'),v=>{ el('kThresh').max=Math.min(160,Math.max(5,Math.round(v/10))); el('kThreshNum').max=el('kThresh').max;});
    bindPair(el('noise'),el('noiseNum'),v=>{ state.gen.sigma=v; });
    bindPair(el('sep'),el('sepNum'),v=>{ state.gen.sep=v; });
    bindPair(el('maxDepth'),el('maxDepthNum'),v=>{ state.tree.maxDepth=Math.round(v); });
    bindPair(el('minLeaf'),el('minLeafNum'),v=>{ state.tree.minLeaf=Math.round(v); });
    el('criterion').addEventListener('change',e=>{ state.tree.criterion=e.target.value; updateEquation(); });
    bindPair(el('kThresh'),el('kThreshNum'),v=>{ state.tree.kThresh=Math.round(v); });
    bindPair(el('classes'),el('classesNum'),v=>{ state.K=Math.round(v); generateData(Math.round(parseFloat(el('points').value))); });
    bindPair(el('clusters'),el('clustersNum'),v=>{ state.C=Math.round(v); generateData(Math.round(parseFloat(el('points').value))); });
    bindPair(el('layout'),el('layoutNum'),v=>{ setLeftPct(v); });

    // ===== Utilities =====
    function hexToRgba(hex,a){ const h=hex.replace('#',''); const bigint=parseInt(h,16); const r=(bigint>>16)&255, g=(bigint>>8)&255, b=bigint&255; return `rgba(${r},${g},${b},${a})`; }

    // ===== Tests =====
    function approxEqual(a,b,eps=1e-3){ return Math.abs(a-b) <= eps; }
    function runTests(){
      const out=[]; const say=(name,ok,msg='')=>out.push(`${ok?'✅':'❌'} ${name}${msg?': '+msg:''}`);
      try{ say('giniCounts pure',[50,0,0] && approxEqual(giniCounts([50,0,0]),0)); }catch(e){ say('giniCounts pure',false,e.message); }
      try{ say('giniCounts 50/50',approxEqual(giniCounts([25,25]),0.5)); }catch(e){ say('giniCounts 50/50',false,e.message); }
      try{ const H=entropyCounts([30,30,40]); say('entropyCounts [30,30,40]', approxEqual(H,1.571)); }catch(e){ say('entropyCounts',false,e.message); }
      try{ const mids=candidateThresholds([1,2,3,4],2); const ok=(mids.length===2 && approxEqual(mids[0],1.5) && approxEqual(mids[1],3.5)); say('candidateThresholds mids',ok,`got [${mids.join(', ')}]`); }catch(e){ say('candidateThresholds mids',false,e.message); }
      try{
        // Backup state
        const backup=JSON.parse(JSON.stringify({state, data:state.data}));
        // Tiny deterministic dataset
        state.K=2; state.tree.kThresh=10; state.tree.minLeaf=1; state.data={x1:[0,0.1,-0.2,2.0,2.1,2.2], x2:[0,0,0,0,0,0], y:[0,0,0,1,1,1]};
        const idx=[0,1,2,3,4,5];
        const node = {leaf:true, idx, rect:fullRect(), counts:[3,3], pred:0, depth:0, parent:null};
        const best=evaluateBestSplit(node);
        const ok = best && best.feat==='x1' && best.nL===3 && best.nR===3;
        say('evaluateBestSplit simple line', ok, best?`feat=${best.feat}, thr=${best.thr.toFixed(2)}`:'no best');
        // restore
        Object.assign(state, backup.state);
        state.data = backup.data;
      }catch(e){ say('evaluateBestSplit simple line',false,e.message); }
      el('testResults').textContent = out.join('\n');
      return out.every(line=>line.startsWith('✅'));
    }

    // ===== Init =====
    window.addEventListener('resize',()=>{ drawAll(); });
    document.getElementById('modeClassic').classList.add('active');
    setLeftPct(parseInt(el('layout').value,10));
    generateData(1200);
    updateEquation();
    buildLegend();

    el('btnRunTests').addEventListener('click', runTests);
    // Auto-run tests once at load
    runTests();
  </script>
</body>
</html>
