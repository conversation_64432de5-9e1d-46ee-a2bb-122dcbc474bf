ML Visual Playground

A small static project to visualize machine learning concepts using HTML/CSS/JS.

Structure:
- index.html — home with links to demos
- gradient-descent.html — interactive gradient descent demo
- neural-network.html — simple forward-pass visualization
- css/styles.css — shared styles
- js/gradient.js & js/gd.bundle.js — gradient logic and browser bundle
- js/neural.js — small NN demo

How to run:
Open `index.html` in your browser. No build step required.

Notes:
This is a starter template. You can enhance the demos by using a plotting library (Chart.js or D3) and adding more controls and math displays.
