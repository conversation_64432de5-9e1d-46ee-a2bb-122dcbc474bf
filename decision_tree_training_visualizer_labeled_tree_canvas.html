<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Decision Tree Training Visualizer — Labeled & Tree Canvas</title>
  <style>
    :root { --bg:#0f172a; --panel:#0b1220; --text:#e5e7eb; --muted:#9ca3af; --accent:#22c55e; --accent2:#60a5fa; --mag:#a78bfa; --danger:#f87171; --warning:#fbbf24; }
    *{box-sizing:border-box}
    body{margin:0;font-family:ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto,Ubuntu;color:var(--text);background:radial-gradient(1000px 600px at 10% -10%,#1f2937,var(--bg))}
    .wrap{max-width:1200px;margin:24px auto;padding:16px}
    .grid{display:grid;grid-template-columns:1.2fr .8fr;gap:16px}
    .card{background:linear-gradient(180deg,rgba(255,255,255,.04),rgba(255,255,255,.02));border:1px solid rgba(255,255,255,.07);border-radius:16px;box-shadow:0 10px 30px rgba(0,0,0,.25)}
    .card h2{margin:0;padding:14px 16px;border-bottom:1px solid rgba(255,255,255,.06);font-size:18px;font-weight:600;color:#f8fafc}
    .card .body{padding:16px}

    /* new: right column stack to host controls + tree */
    .right-col{display:flex;flex-direction:column;gap:16px}

  .canvases{display:grid;gap:12px}
  canvas{width:100%;height:420px;background:#0a1120;border:1px solid rgba(255,255,255,.06);border-radius:16px}
  #metricCanvas{height:180px}
  /* tree canvas defaults to a larger drawing area; will be wrapped in a scrollable container */
  #treeCanvas{height:600px; min-width:720px}

  /* scroll + zoom support for tree */
  .tree-scroll{max-height:520px; overflow:auto; padding:8px; background:linear-gradient(180deg,rgba(255,255,255,.01),transparent); border-radius:12px}
  .tree-wrap{transform-origin:0 0; display:inline-block}
  .tree-zoom-controls{display:flex;gap:8px;justify-content:flex-end;margin-bottom:8px}
  .zoom-btn{padding:6px 8px;border-radius:8px;background:#0b1220;border:1px solid rgba(255,255,255,.06);color:var(--text);cursor:pointer}
  .zoom-display{font-size:12px;color:var(--muted);align-self:center}

    .legend{display:flex;gap:16px;font-size:13px;color:var(--muted);margin-top:8px}
    .dot{display:inline-block;width:10px;height:10px;border-radius:50%;margin-right:6px}
    .dot.c0{background:#60a5fa}
    .dot.c1{background:#f472b6}
    .dot.line{background:var(--accent)}
    .dot.curr{background:#fbbf24}

    .equation{margin-top:10px;font-family:ui-monospace,Consolas,Menlo,monospace;color:#c7d2fe;background:var(--panel);border:1px solid rgba(255,255,255,.08);padding:10px 12px;border-radius:10px}

    .controls{display:grid;grid-template-columns:1fr 1fr;gap:12px}
    .row{display:grid;grid-template-columns:160px 1fr 110px;gap:10px;align-items:center}
    label{color:var(--muted);font-size:12px}
    select,input[type=number],input[type=range]{width:100%;background:#0b1220;color:#e5e7eb;border:1px solid rgba(255,255,255,.08);border-radius:10px;padding:8px 10px}
    input[type=range]{appearance:none;height:6px;border-radius:999px;background:#222a3b}
  input[type=range]::-webkit-slider-thumb{appearance:none;width:16px;height:16px;border-radius:999px;background:var(--accent2);border:2px solid white;box-shadow:0 1px 4px rgba(0,0,0,.4)}
    .btns{display:flex;gap:10px;flex-wrap:wrap;margin-top:8px}
    button{border:1px solid rgba(255,255,255,.12);color:#e5e7eb;background:#101626;padding:10px 14px;border-radius:12px;cursor:pointer;font-weight:600;letter-spacing:.2px;transition:transform .06s ease,background .2s ease,border-color .2s ease}
    button:hover{transform:translateY(-1px);border-color:rgba(255,255,255,.22)}
    .primary{background:linear-gradient(135deg,#1d4ed8,#22c55e);border:none}
    .danger{background:linear-gradient(135deg,#991b1b,#ef4444);border:none}
    .warn{background:linear-gradient(135deg,#92400e,#f59e0b);border:none}
    .muted{background:#0b1220}

    .status{display:grid;grid-template-columns:repeat(3,1fr);gap:10px;margin-top:12px}
    .stat{background:#0b1220;border:1px solid rgba(255,255,255,.08);border-radius:12px;padding:12px}
    .stat .k{color:var(--muted);font-size:12px}
    .stat .v{font-size:18px;font-weight:700}

    .mode{display:flex;gap:8px;align-items:center;margin:8px 0 10px}
    .badge{display:inline-block;padding:4px 8px;border-radius:999px;font-size:12px;border:1px solid rgba(255,255,255,.15)}
    .phase-0{background:#0b3b1d;border-color:#1f7a3e}
    .phase-1{background:#38210b;border-color:#8a5a10}
    .phase-2{background:#2a223f;border-color:#6b5ca3}
    .phase-3{background:#401d2b;border-color:#b0476c}

    .calc-grid{display:grid;grid-template-columns:1fr;gap:10px}
    .calc-box{background:#0b1220;border:1px solid rgba(255,255,255,.1);border-radius:12px;padding:10px}
    .calc-box h3{margin:0 0 6px 0;font-size:14px;color:#dbeafe}
    .mono{font-family:ui-monospace,Consolas,Menlo,monospace;white-space:pre-wrap}
    .scroll{max-height:260px;overflow:auto}

    @media (max-width:980px){.grid{grid-template-columns:1fr}}
  </style>
</head>
<body>
  <div class="wrap">
    <div class="grid">
      <!-- Viz (left column) -->
      <div class="card">
        <h2>Decision Tree — Training Playground <span id="phaseBadge" class="badge phase-0" style="margin-left:8px">Classic mode</span></h2>
        <div class="body">
          <div class="canvases">
            <canvas id="plotCanvas"></canvas>
            <canvas id="metricCanvas"></canvas>
            <!-- treeCanvas moved to the right column -->
          </div>
          <div class="legend">
            <span><span class="dot c0"></span> Class 0</span>
            <span><span class="dot c1"></span> Class 1</span>
            <span><span class="dot line"></span> Splits & labels</span>
            <span><span class="dot curr"></span> Current node region</span>
          </div>
          <div class="equation" id="equation">criterion = Gini: G = 1 − Σ p²,  Gain = Imp(parent) − (nL/n)·Imp(L) − (nR/n)·Imp(R)</div>
        </div>
      </div>

      <!-- Right column: controls + tree (stacked) -->
      <div class="right-col">
        <div class="card">
          <h2>Controls</h2>
          <div class="body">
             <div class="mode">
               <label style="color:var(--muted);font-size:12px">Mode:</label>
               <button id="modeClassic" class="muted">Classic</button>
               <button id="modePhased" class="muted">Multi‑phase</button>
             </div>
             <div class="controls">
               <div class="row"><label># Points</label><input id="points" type="range" min="50" max="3000" value="600" step="50"/><input id="pointsNum" type="number" min="50" max="3000" value="600"/></div>
               <div class="row"><label>Noise σ</label><input id="noise" type="range" min="0" max="3" value="0.6" step="0.01"/><input id="noiseNum" type="number" min="0" max="3" value="0.6" step="0.01"/></div>
               <div class="row"><label>Separation Δ</label><input id="sep" type="range" min="0" max="6" value="3" step="0.1"/><input id="sepNum" type="number" min="0" max="6" value="3" step="0.1"/></div>
               <div class="row"><label>Max depth</label><input id="maxDepth" type="range" min="1" max="10" value="4" step="1"/><input id="maxDepthNum" type="number" min="1" max="10" value="4" step="1"/></div>
               <div class="row"><label>Min samples/leaf</label><input id="minLeaf" type="range" min="1" max="200" value="20" step="1"/><input id="minLeafNum" type="number" min="1" max="200" value="20" step="1"/></div>
               <div class="row"><label>Criterion</label>
                 <select id="criterion">
                   <option value="gini" selected>Gini</option>
                   <option value="entropy">Entropy</option>
                 </select>
               </div>
               <div class="row"><label>Max thresholds/feature</label><input id="kThresh" type="range" min="5" max="100" value="30" step="1"/><input id="kThreshNum" type="number" min="5" max="100" value="30" step="1"/></div>
               <div class="row"><label>Classes</label><input id="numClasses" type="range" min="2" max="8" value="3" step="1"/><input id="numClassesNum" type="number" min="2" max="8" value="3" step="1"/></div>
               <div class="row"><label>Clusters / class</label><input id="clustersPerClass" type="range" min="1" max="6" value="2" step="1"/><input id="clustersPerClassNum" type="number" min="1" max="6" value="2" step="1"/></div>
             </div>
             <div class="btns">
               <button class="primary" id="btnStart">Start</button>
               <button class="muted" id="btnStep">Step</button>
               <button class="muted" id="btnNextPhase" style="display:none">Next Phase</button>
               <button class="warn" id="btnShuffle">Shuffle Data</button>
               <button class="danger" id="btnReset">Reset</button>
               <button class="muted" id="btnRegenerate">Regenerate Dataset</button>
             </div>
             <div class="status">
               <div class="stat"><div class="k">Nodes</div><div class="v" id="statNodes">0</div></div>
               <div class="stat"><div class="k">Depth</div><div class="v" id="statDepth">0</div></div>
               <div class="stat"><div class="k">Accuracy</div><div class="v" id="statAcc">—</div></div>
             </div>
          </div>
        </div>

        <!-- Tree visual now sits as its own card in the right column -->
        <div class="card">
          <h2>Tree Visualization</h2>
          <div class="body">
              <div class="tree-zoom-controls">
                <div style="flex:1"></div>
                <div class="zoom-display" id="zoomDisplay">100%</div>
                <button class="zoom-btn" id="zoomOut" title="Zoom out">−</button>
                <button class="zoom-btn" id="zoomReset" title="Reset zoom">Reset</button>
                <button class="zoom-btn" id="zoomIn" title="Zoom in">+</button>
              </div>
              <div class="tree-scroll" id="treeScroll">
                <div class="tree-wrap" id="treeWrap">
                  <canvas id="treeCanvas"></canvas>
                </div>
              </div>
            </div>
        </div>
      </div>
     </div>
 
     <!-- Calculations -->
     <div id="calcCard" class="card" style="margin-top:16px; display:none">
       <h2>Live Calculations</h2>
       <div class="body calc-grid">
         <div class="calc-box"><h3 id="phaseTitle">Phase 1 — Evaluate candidate splits</h3><div class="mono" id="calcMain">—</div></div>
         <div class="calc-box"><h3>Candidate table (top 60 by gain)</h3><div class="mono scroll" id="tableBox">—</div></div>
       </div>
     </div>
   </div>
 
   <script>
     // ===== Helpers =====
     const el=(id)=>document.getElementById(id);
     const RNG=(seed=1234567)=>{let s=seed>>>0;return()=>{s=(s*1664525+1013904223)>>>0;return s/0xffffffff;}};
     const bindPair=(a,b,fn)=>{const sync=(src,dst)=>{dst.value=src.value;fn(parseFloat(src.value));};a.addEventListener('input',()=>sync(a,b));b.addEventListener('input',()=>sync(b,a));fn(parseFloat(a.value));};
 
     // ===== State =====
    const state={
      seed:42,
      data:{x1:[],x2:[],y:[]}, // y ∈ {0..K-1}
      gen:{sigma:0.6,sep:3,numClasses:3,clustersPerClass:2},
       tree:{maxDepth:4,minLeaf:20,criterion:'gini',kThresh:30},
       model:{nodes:[],root:null},
       queue:[],
       training:{running:false,phase:0},
       metrics:{history:[],acc:NaN},
       scale:{x1min:-5,x1max:5,x2min:-5,x2max:5},
       current:null,
       batchCache:null,
       nodeIdSeq:1
     };
 
     // ===== Data generation =====
     function gaussian(rand){let u=0,v=0;while(u===0)u=rand();while(v===0)v=rand();return Math.sqrt(-2*Math.log(u))*Math.cos(2*Math.PI*v)}
    const CLASS_COLORS = ['#60a5fa','#f472b6','#a78bfa','#f59e0b','#34d399','#fb7185','#f97316','#94a3b8'];
    function generateData(n=600){ const rand=RNG(state.seed); const {sep,sigma,numClasses,clustersPerClass}=state.gen; const x1=[],x2=[],y=[];
      // create cluster centers scattered randomly across space and randomly assign classes
      const centers = [];
      const totalCenters = Math.max(1, numClasses * clustersPerClass);
      for(let i=0;i<totalCenters;i++){
        const angle = rand()*Math.PI*2;
        // allow clusters to appear at variable radii and directions, uncorrelated to class
        const r = sep * (0.2 + rand()*2.2) * (0.6 + (rand()-0.5)*0.6);
        const cx = Math.cos(angle) * r;
        const cy = Math.sin(angle) * r;
        const cSigma = Math.max(0.03, sigma * (0.6 + rand()*1.2));
        const weight = 0.4 + rand()*2.4;
        const cls = Math.floor(rand()*numClasses); // random class assignment so same-class clusters can be scattered
        centers.push({cls, cx, cy, sigma:cSigma, weight});
      }
  // build cumulative weights for weighted sampling of centers
  const weights = centers.map(c=>c.weight); const totalW = weights.reduce((s,v)=>s+v,0); const cum = []; let acc=0; for(const w of weights){ acc+=w; cum.push(acc/totalW); }
      for(let i=0;i<n;i++){
        // weighted sample a center
        const r1 = rand(); let cIdx = 0; while(cIdx < cum.length && r1 > cum[cIdx]) cIdx++;
        if(cIdx>=centers.length) cIdx = centers.length-1;
        const center = centers[cIdx];
        // sample point using the cluster-specific sigma and a tiny uniform jitter
        x1.push(center.cx + gaussian(rand)*center.sigma + (rand()-0.5)*0.35);
        x2.push(center.cy + gaussian(rand)*center.sigma + (rand()-0.5)*0.35);
        y.push(center.cls);
      }
      state.data={x1,x2,y}; fitScale(); resetTree(); updateLegend(); drawAll(); }
     function fitScale(){ const {x1,x2}=state.data; const pad=.2; const x1min=Math.min(...x1),x1max=Math.max(...x1); const x2min=Math.min(...x2),x2max=Math.max(...x2); const xr=x1max-x1min,yr=x2max-x2min; state.scale={x1min:x1min-xr*pad,x1max:x1max+xr*pad,x2min:x2min-yr*pad,x2max:x2max+yr*pad}; }
 
     // ===== Impurity =====
  // multi-class impurity functions (accept counts array or separate args)
  function giniCounts(counts){ const n = counts.reduce((s,v)=>s+v,0); if(n===0) return 0; let sum=0; for(const v of counts){ const p=v/n; sum += p*p; } return 1 - sum; }
  function entropyCounts(counts){ const n = counts.reduce((s,v)=>s+v,0); if(n===0) return 0; const h=(p)=> (p<=0?0: -p*Math.log2(p)); let sum=0; for(const v of counts){ sum += h(v/n); } return sum; }
  function impurityFromCounts(counts){ return state.tree.criterion==='gini'? giniCounts(counts) : entropyCounts(counts); }
 
     // ===== Tree structures =====
    function makeLeaf(idx){ const counts = Array(state.gen.numClasses).fill(0); for(const i of idx){ counts[state.data.y[i]] = (counts[state.data.y[i]]||0) + 1; }
      const pred = counts.indexOf(Math.max(...counts));
      const n={id:state.nodeIdSeq++, leaf:true, idx, rect:null, counts, pred, depth:0, parent:null};
       return n;
     }
     function resetTree(){ state.nodeIdSeq=1; const idx=[...Array(state.data.y.length).keys()]; const root=makeLeaf(idx); root.rect=fullRect(); state.model={nodes:[root],root}; state.queue=[root]; state.current=root; state.training.phase=0; state.metrics.history=[]; updateStats(); }
     function fullRect(){ const {x1min,x1max,x2min,x2max}=state.scale; return {x1min,x1max,x2min,x2max}; }
 
     // ===== Split search =====
     function uniqueSorted(values){ return Array.from(new Set(values)).sort((a,b)=>a-b); }
    function candidateThresholds(vals,k){ const uniq=uniqueSorted(vals); if(uniq.length<=1) return []; const mids=[]; for(let i=0;i<uniq.length-1;i++){ mids.push((uniq[i]+uniq[i+1])/2); } if(mids.length<=k) return mids; const step=(mids.length-1)/(k-1); return Array.from({length:k},(_,i)=>mids[Math.round(i*step)]); }
    function evaluateBestSplit(node){ const idx=node.idx; const n=idx.length; if(n<2) return null; const K = state.gen.numClasses; const parentCounts = Array(K).fill(0); for(const i of idx){ parentCounts[state.data.y[i]]++; } const impP=impurityFromCounts(parentCounts); const feats=[['x1',state.data.x1],['x2',state.data.x2]]; let best=null; const rows=[];
      for(const [feat,arr] of feats){ const values=idx.map(i=>arr[i]); const ths=candidateThresholds(values,state.tree.kThresh); for(const thr of ths){ const lCounts=Array(K).fill(0), rCounts=Array(K).fill(0); for(const i of idx){ const y=state.data.y[i]; const goLeft=(arr[i]<=thr); if(goLeft) lCounts[y]++; else rCounts[y]++; } const nL=lCounts.reduce((s,v)=>s+v,0), nR=rCounts.reduce((s,v)=>s+v,0); if(nL<state.tree.minLeaf || nR<state.tree.minLeaf) continue; const impL=impurityFromCounts(lCounts), impR=impurityFromCounts(rCounts); const gain=impP - (nL/n)*impL - (nR/n)*impR; rows.push({feat,thr,nL,nR,lCounts,rCounts,impP,impL,impR,gain}); if(!best || gain>best.gain){ best={feat,thr,gain,nL,nR,lCounts,rCounts,impP,impL,impR}; } } }
      if(!best) return null; const arr = best.feat==='x1'? state.data.x1 : state.data.x2; const leftIdx=[], rightIdx=[]; for(const i of idx){ (arr[i] <= best.thr? leftIdx:rightIdx).push(i); } return { ...best, leftIdx, rightIdx, rows: rows.sort((a,b)=>b.gain-a.gain).slice(0,200) } }
 
     // ===== Training =====
     function canSplit(node){ if(node.leaf===false) return false; if(node.idx.length < 2*state.tree.minLeaf) return false; if(node.depth >= state.tree.maxDepth) return false; return !(node.c0===0 || node.c1===0); }
     function nextSplittableNode(){ while(state.queue.length){ const n=state.queue.shift(); if(canSplit(n)) return n; } return null; }
     function classicStep(){ if(state.queue.length===0) return finishEpoch(); const node=nextSplittableNode(); if(!node){ return finishEpoch(); } const best=evaluateBestSplit(node); if(!best){ node.leaf=true; return; } applySplit(node,best); updateStats(); drawAll(); }
     function finishEpoch(){ const acc=accuracy(); state.metrics.history.push(acc); updateStats(); }
     function applySplit(node,best){ node.leaf=false; node.feat=best.feat; node.thr=best.thr; node.gain=best.gain; const left=makeLeaf(best.leftIdx), right=makeLeaf(best.rightIdx); left.depth=node.depth+1; right.depth=node.depth+1; left.parent=node; right.parent=node; const r=node.rect; if(best.feat==='x1'){ left.rect={x1min:r.x1min,x1max:best.thr,x2min:r.x2min,x2max:r.x2max}; right.rect={x1min:best.thr,x1max:r.x1max,x2min:r.x2min,x2max:r.x2max}; } else { left.rect={x1min:r.x1min,x1max:r.x1max,x2min:r.x2min,x2max:best.thr}; right.rect={x1min:r.x1min,x1max:r.x1max,x2min:best.thr,x2max:r.x2max}; } node.left=left; node.right=right; state.model.nodes.push(left,right); state.queue.push(left,right); state.current=left; }
 
     // ===== Phased engine =====
     function nextPhase(){ switch(state.training.phase){ case 0: const node = state.current && canSplit(state.current)? state.current : nextSplittableNode(); if(!node){ finishEpoch(); break; } state.current=node; const best=evaluateBestSplit(node); state.batchCache={node,best}; renderPhase(0,{node,best}); state.training.phase=1; break; case 1: if(!state.batchCache || !state.batchCache.best){ state.training.phase=0; break; } renderPhase(1,{best:state.batchCache.best}); state.training.phase=2; break; case 2: if(!state.batchCache || !state.batchCache.best){ state.training.phase=0; break; } applySplit(state.batchCache.node,state.batchCache.best); renderPhase(2,{applied:true}); state.training.phase=3; break; case 3: const acc=accuracy(); state.metrics.history.push(acc); updateStats(); drawAll(); renderPhase(3,{acc}); state.batchCache=null; state.training.phase=0; break; } updatePhaseUI(); }
 
     // ===== Metrics =====
  function predictOne(i){ let n=state.model.root; while(n && n.leaf===false){ const feat = n.feat==='x1'? state.data.x1[i] : state.data.x2[i]; n = (feat <= n.thr)? n.left : n.right; } return n?n.pred:0; }
     function accuracy(){ const N=state.data.y.length; let ok=0; for(let i=0;i<N;i++){ if(predictOne(i)===state.data.y[i]) ok++; } return ok/N; }
 
     // ===== Rendering =====
  const plot=el('plotCanvas'); const metr=el('metricCanvas'); const tree=el('treeCanvas'); const pctx=plot.getContext('2d'); const mctx=metr.getContext('2d'); const tctx=tree.getContext('2d');
  const treeWrap = el('treeWrap'); const treeScroll = el('treeScroll'); const zoomDisplay = el('zoomDisplay');
     function mapX1(x){const {x1min,x1max}=state.scale; return (x-x1min)/(x1max-x1min)*(plot.width-40)+20}
     function mapX2(y){const {x2min,x2max}=state.scale; return (1-(y-x2min)/(x2max-x2min))*(plot.height-40)+20}
     function clearCanvas(c){const ctx=c.getContext('2d'); ctx.clearRect(0,0,c.width,c.height);}
     function drawGrid(ctx,w,h){ctx.save();ctx.strokeStyle='rgba(255,255,255,.06)';ctx.lineWidth=1;for(let x=40;x<w;x+=40){ctx.beginPath();ctx.moveTo(x,0);ctx.lineTo(x,h);ctx.stroke();}for(let y=40;y<h;y+=40){ctx.beginPath();ctx.moveTo(0,y);ctx.lineTo(w,y);ctx.stroke();}ctx.restore();}
     // resize canvases. For the tree canvas we must use the canvas's CSS-layout size BEFORE CSS scale on the wrapper;
     // we compute the width/height from treeWrap's bounding rect and multiply by devicePixelRatio.
     function resizeCanvases(){const dpr=window.devicePixelRatio||1;
       // plot and metric use their DOM rects
       [plot,metr].forEach(c=>{const rect=c.getBoundingClientRect(); c.width=Math.floor(rect.width*dpr); c.height=Math.floor(rect.height*dpr); c.getContext('2d').setTransform(dpr,0,0,dpr,0,0);});
       // tree: measure the inner wrap's size (untransformed) so drawing matches visual scaling
  // use untransformed layout sizes from the wrap to determine canvas pixel size
  const wrapW = Math.max(16, treeWrap.offsetWidth || treeWrap.clientWidth);
  const wrapH = Math.max(16, treeWrap.offsetHeight || treeWrap.clientHeight);
  const w = Math.floor(wrapW * dpr);
  const h = Math.floor(wrapH * dpr);
  tree.width = w; tree.height = h; tctx.setTransform(dpr,0,0,dpr,0,0);
     }

     // zoom state (1.0 == 100%) and helpers
     state.treeZoom = 1.0;
     function setZoom(z){ state.treeZoom = Math.max(0.2, Math.min(3, z)); treeWrap.style.transform = `scale(${state.treeZoom})`; zoomDisplay.textContent = Math.round(state.treeZoom*100)+'%'; }
     function zoomIn(){ setZoom(state.treeZoom * 1.2); resizeCanvases(); drawAll(); }
     function zoomOut(){ setZoom(state.treeZoom / 1.2); resizeCanvases(); drawAll(); }
     function zoomReset(){ setZoom(1.0); resizeCanvases(); drawAll(); }
 
  function drawAll(){ resizeCanvases(); drawSurface(); drawPoints(); drawSplits(); drawMetrics(); drawTreeCanvas(); updateEquation(); }
 
     function drawSurface(){ const w=plot.width/(window.devicePixelRatio||1), h=plot.height/(window.devicePixelRatio||1); clearCanvas(plot); drawGrid(pctx,w,h); pctx.strokeStyle='rgba(255,255,255,.25)'; pctx.lineWidth=1.5; pctx.strokeRect(20,20,w-40,h-40); const cols=140, rows=100; const {x1min,x1max,x2min,x2max}=state.scale; const dx=(x1max-x1min)/cols, dy=(x2max-x2min)/rows; for(let r=0;r<rows;r++){ for(let c=0;c<cols;c++){ const x1=x1min + (c+0.5)*dx; const x2=x2min + (r+0.5)*dy; let n=state.model.root; while(n && n.leaf===false){ const f=(n.feat==='x1'? x1:x2); n=(f<=n.thr)? n.left:n.right; } const cls=n?n.pred:0; pctx.fillStyle= cls? 'rgba(244,114,182,0.12)' : 'rgba(96,165,250,0.12)'; pctx.fillRect(mapX1(x1)-0.5, mapX2(x2)-0.5, 2, 2); }} }
 
  function drawPoints(){ const N=state.data.y.length; for(let i=0;i<N;i++){ const xi1=state.data.x1[i], xi2=state.data.x2[i], yi=state.data.y[i]; pctx.beginPath(); pctx.arc(mapX1(xi1), mapX2(xi2), 2.8, 0, Math.PI*2); pctx.fillStyle = CLASS_COLORS[yi % CLASS_COLORS.length] || '#999'; pctx.fill(); } }
 
  function drawSplitLabel(txt,x,y){ pctx.save(); pctx.font='12px ui-monospace,Consolas,Menlo,monospace'; const pad=4; const w=pctx.measureText(txt).width+pad*2; const h=18; pctx.fillStyle='rgba(15,23,42,0.85)'; pctx.strokeStyle='rgba(255,255,255,0.25)'; pctx.lineWidth=1; pctx.beginPath(); pctx.roundRect ? pctx.roundRect(x-w/2,y-h/2,w,h,6) : pctx.rect(x-w/2,y-h/2,w,h); pctx.fill(); pctx.stroke(); pctx.fillStyle='#e5e7eb'; pctx.textAlign='center'; pctx.textBaseline='middle'; pctx.fillText(txt,x,y+0.5); pctx.restore(); }
 
     function drawSplits(){ pctx.save(); function drawNode(n){ if(!n) return; if(n===state.current){ pctx.fillStyle='rgba(251,191,36,0.10)'; const r=n.rect; pctx.fillRect(mapX1(r.x1min), mapX2(r.x2max), mapX1(r.x1max)-mapX1(r.x1min), mapX2(r.x2min)-mapX2(r.x2max)); }
         if(n.leaf===false){ const r=n.rect; pctx.strokeStyle='#22c55e'; pctx.lineWidth=2; if(n.feat==='x1'){ const x=mapX1(n.thr); const yTop=mapX2(r.x2max), yBot=mapX2(r.x2min); pctx.beginPath(); pctx.moveTo(x,yTop); pctx.lineTo(x,yBot); pctx.stroke(); const midY=(yTop+yBot)/2; drawSplitLabel(`x1 ≤ ${n.thr.toFixed(3)}`, x-6, midY-12); drawSplitLabel(`x1 > ${n.thr.toFixed(3)}`, x+6, midY+12); } else { const y=mapX2(n.thr); const xL=mapX1(r.x1min), xR=mapX1(r.x1max); pctx.beginPath(); pctx.moveTo(xL,y); pctx.lineTo(xR,y); pctx.stroke(); const midX=(xL+xR)/2; drawSplitLabel(`x2 ≤ ${n.thr.toFixed(3)}`, midX-30, y-10); drawSplitLabel(`x2 > ${n.thr.toFixed(3)}`, midX+30, y+10); }
           drawNode(n.left); drawNode(n.right); }
       }
       drawNode(state.model.root); pctx.restore(); }
 
     function drawMetrics(){ clearCanvas(metr); const w=metr.width/(window.devicePixelRatio||1), h=metr.height/(window.devicePixelRatio||1); drawGrid(mctx,w,h); mctx.strokeStyle='rgba(255,255,255,.25)'; mctx.lineWidth=1.5; mctx.strokeRect(20,20,w-40,h-40); if(state.metrics.history.length===0) return; const padL=30,padR=20,padT=20,padB=30; const xmin=0,xmax=state.metrics.history.length-1; const ymin=Math.min(...state.metrics.history),ymax=1; const mapLX=(x)=>padL+(x-xmin)/Math.max(1,(xmax-xmin))*(w-padL-padR); const mapLY=(y)=>padT+(1-(y-ymin)/Math.max(1e-9,(ymax-ymin)))*(h-padT-padB); mctx.strokeStyle='#22c55e'; mctx.lineWidth=2; mctx.beginPath(); mctx.moveTo(mapLX(0),mapLY(state.metrics.history[0])); for(let i=1;i<state.metrics.history.length;i++){ mctx.lineTo(mapLX(i),mapLY(state.metrics.history[i])); } mctx.stroke(); mctx.fillStyle='rgba(229,231,235,.9)'; mctx.font='12px ui-sans-serif,system-ui'; mctx.fillText('Step',w/2-14,h-8); mctx.save(); mctx.translate(8,h/2+20); mctx.rotate(-Math.PI/2); mctx.fillText('Accuracy',0,0); mctx.restore(); }
 
     // ===== Tree canvas =====
     function layoutTree(root){ const levels=[]; const leaves=[]; (function collect(n,d){ if(!n) return; n.depth=d; if(n.leaf){ leaves.push(n); } else { collect(n.left,d+1); collect(n.right,d+1); } if(!levels[d]) levels[d]=[]; levels[d].push(n); })(root,0); const W=tree.width/(window.devicePixelRatio||1), H=tree.height/(window.devicePixelRatio||1); const padX=40, padY=30; const cols=Math.max(1,leaves.length); const colStep=(W-2*padX)/Math.max(1,cols-1); // assign x to leaves by order
       let order=0; (function setX(n){ if(!n) return; if(n.leaf){ n._tx=padX + order*colStep; order++; } else { setX(n.left); setX(n.right); n._tx = n.left && n.right ? (n.left._tx + n.right._tx)/2 : (n.left? n.left._tx : (n.right? n.right._tx : padX)); } })(root); (function setY(n){ if(!n) return; n._ty = padY + n.depth * ((H-2*padY)/Math.max(1,(levels.length-1))); setY(n.left); setY(n.right); })(root); }
 
     function drawTreeCanvas(){ const W=tree.width/(window.devicePixelRatio||1), H=tree.height/(window.devicePixelRatio||1); clearCanvas(tree); tctx.fillStyle='#0a1120'; tctx.fillRect(0,0,W,H); tctx.strokeStyle='rgba(255,255,255,.25)'; tctx.strokeRect(0,0,W,H); const root=state.model.root; if(!root) return; layoutTree(root);
       // edges
       tctx.lineWidth=1.5; function edge(a,b,label){ tctx.strokeStyle='rgba(255,255,255,.25)'; tctx.beginPath(); tctx.moveTo(a._tx,a._ty+16); tctx.lineTo(b._tx,b._ty-16); tctx.stroke(); if(label){ drawTreeEdgeLabel(label,(a._tx+b._tx)/2,(a._ty+b._ty)/2); } }
       (function walkEdges(n){ if(!n || n.leaf) return; const lLabel = n.feat==='x1' ? `≤ ${n.thr.toFixed(3)}` : `≤ ${n.thr.toFixed(3)}`; const rLabel = n.feat==='x1' ? `> ${n.thr.toFixed(3)}` : `> ${n.thr.toFixed(3)}`; edge(n,n.left,lLabel); edge(n,n.right,rLabel); walkEdges(n.left); walkEdges(n.right); })(root);
       // nodes
  function nodeBox(n){ const txtTop = n.leaf? `leaf • pred=${n.pred}` : `${n.feat} ≤ ${n.thr.toFixed(3)}`; const countsText = (n.counts && n.counts.length)? n.counts.map((c,i)=>`c${i}=${c}`).join(', ') : `n=${n.idx.length}`; const txtBot = countsText; const w = Math.max(140, 10* Math.max(txtTop.length, txtBot.length)/1.6); const h = 48; const x=n._tx - w/2, y=n._ty - h/2; tctx.fillStyle = n===state.current? 'rgba(251,191,36,0.12)' : 'rgba(255,255,255,0.04)'; tctx.strokeStyle='rgba(255,255,255,0.18)'; tctx.lineWidth=1; tctx.beginPath(); tctx.roundRect ? tctx.roundRect(x,y,w,h,10) : tctx.rect(x,y,w,h); tctx.fill(); tctx.stroke(); tctx.fillStyle='#e5e7eb'; tctx.font='12px ui-monospace,Consolas,Menlo,monospace'; tctx.textBaseline='middle'; tctx.textAlign='center'; tctx.fillText(txtTop, n._tx, n._ty-8); tctx.fillStyle='var(--muted)'; tctx.fillText(txtBot, n._tx, n._ty+8); }
       (function walkNodes(n){ if(!n) return; nodeBox(n); walkNodes(n.left); walkNodes(n.right); })(root);
     }
     function drawTreeEdgeLabel(txt,x,y){ tctx.save(); tctx.font='11px ui-monospace,Consolas,Menlo,monospace'; const pad=4; const w=tctx.measureText(txt).width+pad*2; const h=16; tctx.fillStyle='rgba(15,23,42,0.85)'; tctx.strokeStyle='rgba(255,255,255,0.25)'; tctx.beginPath(); tctx.roundRect ? tctx.roundRect(x-w/2,y-h/2,w,h,6) : tctx.rect(x-w/2,y-h/2,w,h); tctx.fill(); tctx.stroke(); tctx.fillStyle='#e5e7eb'; tctx.textAlign='center'; tctx.textBaseline='middle'; tctx.fillText(txt,x,y+0.5); tctx.restore(); }
 
     // ===== Equation / Stats =====
     function updateEquation(){ const crit=state.tree.criterion==='gini'? 'Gini: G=1−Σp²' : 'Entropy: H=−Σ p log₂ p'; el('equation').textContent=`criterion = ${crit},  Gain = Imp(parent) − (nL/n)·Imp(L) − (nR/n)·Imp(R)`; }
     function updateStats(){ const nodes=state.model.nodes.length; let depth=0; (function walk(n){ if(!n) return; depth=Math.max(depth,n.depth); if(n.leaf===false){ walk(n.left); walk(n.right);} })(state.model.root); const acc=accuracy(); state.metrics.acc=acc; el('statNodes').textContent=String(nodes); el('statDepth').textContent=String(depth); el('statAcc').textContent=(isFinite(acc)?acc.toFixed(3):'—'); }
 
     // ===== Calculations UI =====
     function renderTable(rows){
       const K = state.gen.numClasses;
       // header with dynamic per-class columns
       const classColsL = Array.from({length:K},(_,i)=>`l${i}`).join('\t');
       const classColsR = Array.from({length:K},(_,i)=>`r${i}`).join('\t');
       const header = ['feat','thr','nL','nR',classColsL,classColsR,'Imp(P)','Imp(L)','Imp(R)','Gain'].join('\t') + '\n' + '-'.repeat(200) + '\n';
       const pad=(v)=> (Number.isFinite(v)?v.toFixed(6):'—').padStart(10,' ');
       const lines = rows.slice(0,80).map(r=>{
         const perL = (r.lCounts||[]).map(v=>String(v).padStart(4)).join('\t');
         const perR = (r.rCounts||[]).map(v=>String(v).padStart(4)).join('\t');
         return `${r.feat.padEnd(3,' ')}\t${pad(r.thr)}\t${String(r.nL).padStart(4)}\t${String(r.nR).padStart(4)}\t${perL}\t${perR}\t${pad(r.impP)}\t${pad(r.impL)}\t${pad(r.impR)}\t${pad(r.gain)}`;
       });
       el('tableBox').textContent = header + lines.join('\n');
     }

     function renderPhase(p,info){
       const titles=['Phase 1 — Evaluate candidate splits','Phase 2 — Best split calculations','Phase 3 — Apply split','Phase 4 — Update metrics'];
       el('phaseTitle').textContent=titles[p];
       const f=(x)=>Number.isFinite(x)?x.toFixed(6):'—';
       if(p===0){ const {node,best}=info; if(!best){ el('calcMain').textContent='No valid split (minLeaf/depth/purity reached).'; el('tableBox').textContent='—'; return; } const head=`Node depth=${node.depth}, size=${node.idx.length}\nParent Impurity = ${f(best.impP)}\nCandidates (top by gain):`; el('calcMain').textContent=head; renderTable(best.rows); }
       if(p===1){ const {best}=info; if(!best){ el('calcMain').textContent='—'; return; } const n=best.nL+best.nR;
         // step-by-step numeric calculation
         const lines = [];
         lines.push(`Best: feat=${best.feat}, thr=${f(best.thr)}`);
         // parent counts per class
         const parentCounts = best.parentCounts || (function(){ const arr=Array(state.gen.numClasses).fill(0); for(const i of best.leftIdx.concat(best.rightIdx||[])){ arr[state.data.y[i]]++; } return arr; })();
         lines.push(`Parent counts (per-class): [${(parentCounts||[]).join(', ')}], n=${n}`);
         // parent p_i and contributions
         if(parentCounts){ const sumP = parentCounts.reduce((s,v)=>s+v,0); if(sumP>0){ const pvals = parentCounts.map(v=>(v/sumP).toFixed(6)); lines.push(`Parent p_i: [${pvals.join(', ')}]`); if(state.tree.criterion==='gini'){ const contribs = parentCounts.map(v=>{ const p=v/sumP; return (p*p).toFixed(6); }); lines.push(`Gini contribution Σp_i^2 = ${contribs.join(' + ')} = ${ (parentCounts.reduce((s,v)=>s+ (Math.pow(v/sumP,2)),0)).toFixed(6) }`); lines.push(`Imp(P) = 1 - Σp_i^2 = ${ (1 - parentCounts.reduce((s,v)=>s+ (Math.pow(v/sumP,2)),0)).toFixed(6) }`); } else { const contribs = parentCounts.map(v=>{ const p=v/sumP; return (p<=0?0:(-p*Math.log2(p))).toFixed(6); }); lines.push(`Entropy contribution Σ -p_i log2 p_i = ${contribs.join(' + ')} = ${ parentCounts.reduce((s,v)=>s + (v===0?0: - (v/sumP)*Math.log2(v/sumP)),0).toFixed(6) }`); lines.push(`Imp(P) = ${ parentCounts.reduce((s,v)=>s + (v===0?0: - (v/sumP)*Math.log2(v/sumP)),0).toFixed(6) }`); } } }
         // left counts and impurity
         lines.push(`Left (nL=${best.nL}): counts = [${(best.lCounts||[]).join(', ')}]`);
         if(best.lCounts){ const lc = best.lCounts; const sumL = lc.reduce((s,v)=>s+v,0); if(sumL>0){ const pvalsL = lc.map(v=>(v/sumL).toFixed(6)); lines.push(`Left p_i: [${pvalsL.join(', ')}]`); if(state.tree.criterion==='gini'){ const sumSq = lc.reduce((s,v)=>s + Math.pow(v/sumL,2),0); lines.push(`Imp(L) = 1 - Σ p_i^2 = 1 - ${sumSq.toFixed(6)} = ${ (1 - sumSq).toFixed(6) }`); } else { const ent = lc.reduce((s,v)=>s + (v===0?0: - (v/sumL)*Math.log2(v/sumL)),0); lines.push(`Imp(L) = Σ -p_i log2 p_i = ${ ent.toFixed(6) }`); } } }
         // right counts and impurity
         lines.push(`Right (nR=${best.nR}): counts = [${(best.rCounts||[]).join(', ')}]`);
         if(best.rCounts){ const rc = best.rCounts; const sumR = rc.reduce((s,v)=>s+v,0); if(sumR>0){ const pvalsR = rc.map(v=>(v/sumR).toFixed(6)); lines.push(`Right p_i: [${pvalsR.join(', ')}]`); if(state.tree.criterion==='gini'){ const sumSqR = rc.reduce((s,v)=>s + Math.pow(v/sumR,2),0); lines.push(`Imp(R) = 1 - Σ p_i^2 = 1 - ${sumSqR.toFixed(6)} = ${ (1 - sumSqR).toFixed(6) }`); } else { const entr = rc.reduce((s,v)=>s + (v===0?0: - (v/sumR)*Math.log2(v/sumR)),0); lines.push(`Imp(R) = Σ -p_i log2 p_i = ${ entr.toFixed(6) }`); } } }
         // weighted combination and final gain
         const nL = best.nL, nR = best.nR;
         const impL = best.impL, impR = best.impR, impP = best.impP;
         const weighted = (nL/n)*impL + (nR/n)*impR;
         lines.push(`Weighted children impurity = (nL/n)*Imp(L) + (nR/n)*Imp(R) = (${nL}/${n})*${impL.toFixed(6)} + (${nR}/${n})*${impR.toFixed(6)} = ${weighted.toFixed(6)}`);
         lines.push(`Gain = Imp(P) - weighted = ${impP.toFixed(6)} - ${weighted.toFixed(6)} = ${ (impP - weighted).toFixed(6) }`);
         el('calcMain').textContent = lines.join('\n'); }
       if(p===2){ el('calcMain').textContent='Applied split: new left/right children added to queue.'; }
       if(p===3){ el('calcMain').textContent=`Accuracy snapshot = ${f(info.acc)}`; }
     }
     function updatePhaseUI(){ const badge=el('phaseBadge'); if(!state.training.running && el('modeClassic').classList.contains('active')){ badge.textContent='Classic mode'; badge.className='badge phase-0'; el('btnNextPhase').style.display='none'; el('calcCard').style.display='none'; } else if(el('modePhased').classList.contains('active')){ const titles=['Phase 1 — Evaluate candidate splits','Phase 2 — Best split calculations','Phase 3 — Apply split','Phase 4 — Update metrics']; badge.textContent=titles[state.training.phase]; badge.className='badge phase-'+state.training.phase; el('btnNextPhase').style.display='inline-block'; el('calcCard').style.display='block'; } else { badge.textContent='Classic mode'; badge.className='badge phase-0'; el('btnNextPhase').style.display='none'; el('calcCard').style.display='none'; } updateEquation(); drawAll(); }
 
     // ===== Events =====
     // wire zoom controls
     el('zoomIn').addEventListener('click',()=>{ zoomIn(); });
     el('zoomOut').addEventListener('click',()=>{ zoomOut(); });
     el('zoomReset').addEventListener('click',()=>{ zoomReset(); });
     // allow ctrl+wheel (or wheel) over treeScroll to zoom; otherwise scroll
     treeScroll.addEventListener('wheel', (ev)=>{
       if(ev.ctrlKey || ev.metaKey){ ev.preventDefault(); const delta = ev.deltaY; if(delta>0) zoomOut(); else zoomIn(); }
     }, {passive:false});
     function setMode(mode){ el('modeClassic').classList.toggle('active', mode==='classic'); el('modePhased').classList.toggle('active', mode==='phased'); state.training.phase=0; state.training.running=false; updatePhaseUI(); }
     el('modeClassic').addEventListener('click',()=>setMode('classic'));
     el('modePhased').addEventListener('click',()=>setMode('phased'));
     el('btnRegenerate').addEventListener('click',()=>{ generateData(Math.round(parseFloat(el('points').value))); });
     el('btnShuffle').addEventListener('click',()=>{ state.seed=Math.floor(Math.random()*1e9); generateData(Math.round(parseFloat(el('points').value))); });
     el('btnReset').addEventListener('click',()=>{ resetTree(); drawAll(); el('calcMain').textContent='—'; el('tableBox').textContent='—'; state.metrics.history=[]; });
     el('btnStart').addEventListener('click',(e)=>{ const phased = el('modePhased').classList.contains('active'); state.training.running=!state.training.running; e.target.textContent=state.training.running?'Pause':'Start'; if(state.training.running){ (function loop(){ if(!state.training.running) return; if(phased){ nextPhase(); } else { classicStep(); } requestAnimationFrame(loop); })(); }});
     el('btnStep').addEventListener('click',()=>{ const phased = el('modePhased').classList.contains('active'); if(!state.training.running){ if(phased){ nextPhase(); } else { classicStep(); } }});
     el('btnNextPhase').addEventListener('click',()=>{ if(!state.training.running) nextPhase(); });
 
     // ===== Bind controls =====
     bindPair(el('points'),el('pointsNum'),v=>{ el('kThresh').max=Math.min(100,Math.max(5,Math.round(v/10))); el('kThreshNum').max=el('kThresh').max;});
     bindPair(el('noise'),el('noiseNum'),v=>{ state.gen.sigma=v; });
     bindPair(el('sep'),el('sepNum'),v=>{ state.gen.sep=v; });
     bindPair(el('maxDepth'),el('maxDepthNum'),v=>{ state.tree.maxDepth=Math.round(v); });
     bindPair(el('minLeaf'),el('minLeafNum'),v=>{ state.tree.minLeaf=Math.round(v); });
     el('criterion').addEventListener('change',e=>{ state.tree.criterion=e.target.value; updateEquation(); });
     bindPair(el('kThresh'),el('kThreshNum'),v=>{ state.tree.kThresh=Math.round(v); });
    // Classes and clusters controls (multi-class support)
    bindPair(el('numClasses'), el('numClassesNum'), v => { state.gen.numClasses = Math.max(2, Math.round(v)); });
    bindPair(el('clustersPerClass'), el('clustersPerClassNum'), v => { state.gen.clustersPerClass = Math.max(1, Math.round(v)); });

    function updateLegend(){ const legend = document.querySelector('.legend'); if(!legend) return; // rebuild legend entries for classes
      legend.innerHTML = '';
      for(let i=0;i<state.gen.numClasses;i++){ const span = document.createElement('span'); const dot = document.createElement('span'); dot.className = 'dot c'+i; dot.style.background = CLASS_COLORS[i % CLASS_COLORS.length]; span.appendChild(dot); span.appendChild(document.createTextNode(' Class '+i)); legend.appendChild(span); }
      const extra1 = document.createElement('span'); const d1 = document.createElement('span'); d1.className='dot line'; extra1.appendChild(d1); extra1.appendChild(document.createTextNode(' Splits & labels')); legend.appendChild(extra1);
      const extra2 = document.createElement('span'); const d2 = document.createElement('span'); d2.className='dot curr'; extra2.appendChild(d2); extra2.appendChild(document.createTextNode(' Current node region')); legend.appendChild(extra2);
    }

    // ensure legend and dataset update when relevant controls change
    el('numClasses').addEventListener('input', ()=>{ updateLegend(); });
    el('clustersPerClass').addEventListener('input', ()=>{ /* visual only; regenerate on dataset create */ });
    // update legend initially
    updateLegend();
 
     // ===== Init =====
  window.addEventListener('resize',()=>{ drawAll(); });
  // observe size changes to treeWrap so canvas can update when content size changes
  const ro = new ResizeObserver(()=>{ drawAll(); }); ro.observe(treeWrap);
     generateData(600);
     setMode('classic');
     updateEquation();
   </script>
 </body>
 </html>
