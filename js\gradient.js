// gradient.js - simple gradient descent visualization (no deps)
(function(){
  // function f(x) = x^2 + 5
  function f(x){return x*x + 5}
  function df(x){return 2*x}

  const container = document.getElementById('gd-canvas');
  const ctx = (function(){
    // using a simple canvas
    const c = document.createElement('canvas');
    c.width = container.clientWidth - 20 || 760;
    c.height = 520;
    container.appendChild(c);
    return c.getContext('2d');
  })();

  // UI elements
  const lrEl = document.getElementById('lr');
  const lrVal = document.getElementById('lr-val');
  const startEl = document.getElementById('start');
  const startVal = document.getElementById('start-val');
  const itersEl = document.getElementById('iters');
  const itersVal = document.getElementById('iters-val');
  const speedEl = document.getElementById('speed');
  const speedVal = document.getElementById('speed-val');
  const mathBox = document.getElementById('math');
  const iterLog = document.getElementById('iter-log');
  const playBtn = document.getElementById('play');
  const stepBtn = document.getElementById('step');
  const resetBtn = document.getElementById('reset');

  function drawAxes(){
    const w=ctx.canvas.width;const h=ctx.canvas.height;
    ctx.clearRect(0,0,w,h);
    ctx.fillStyle='#071521';ctx.fillRect(0,0,w,h);
    // grid
    ctx.strokeStyle='rgba(255,255,255,0.03)';ctx.beginPath();
    for(let i=0;i<10;i++){const x=i*(w/10);ctx.moveTo(x,0);ctx.lineTo(x,h)}
    for(let j=0;j<10;j++){const y=j*(h/10);ctx.moveTo(0,y);ctx.lineTo(w,y)}
    ctx.stroke();
  }

  function drawFunction(){
    const w=ctx.canvas.width;const h=ctx.canvas.height;
    // map x in -10..10 to px 0..w ; y from 0..110 to h..0
    ctx.strokeStyle='#38c6ff';ctx.lineWidth=2;ctx.beginPath();
    for(let i=0;i<=w;i++){
      const x = (i/w)*20 - 10;
      const y = f(x);
      const py = h - (y/110)*h;
      if(i===0) ctx.moveTo(i,py); else ctx.lineTo(i,py);
    }
    ctx.stroke();
  }

  function drawPoint(x,color='lime',size=8){
    const w=ctx.canvas.width;const h=ctx.canvas.height;
    const px = ((x+10)/20)*w;
    const py = h - (f(x)/110)*h;
    ctx.fillStyle=color;ctx.beginPath();ctx.arc(px,py,size,0,Math.PI*2);ctx.fill();
  }

  function drawTangentAt(x,color='orange'){
    const w=ctx.canvas.width;const h=ctx.canvas.height;
    const slope = df(x);
    const x0 = x - 4; const x1 = x + 4;
    const ty0 = f(x) + slope*(x0 - x);
    const ty1 = f(x) + slope*(x1 - x);
    const px0 = ((x0+10)/20)*w; const py0 = h - (ty0/110)*h;
    const px1 = ((x1+10)/20)*w; const py1 = h - (ty1/110)*h;
    ctx.save(); ctx.setLineDash([8,6]); ctx.strokeStyle = color; ctx.lineWidth = 2; ctx.beginPath();
    ctx.moveTo(px0,py0); ctx.lineTo(px1,py1); ctx.stroke(); ctx.restore();
  }

  function drawNextPoint(xNext){
    const w=ctx.canvas.width;const h=ctx.canvas.height;
    const pxCur = ((state.x+10)/20)*w;
    const pyCur = h - (f(state.x)/110)*h;
    const pxNext = ((xNext+10)/20)*w;
    const pyNext = h - (f(xNext)/110)*h;
    ctx.save(); ctx.strokeStyle='#9b7bff'; ctx.fillStyle='#9b7bff'; ctx.lineWidth=2;
    ctx.beginPath(); ctx.moveTo(pxCur,pyCur); ctx.lineTo(pxNext,pyNext); ctx.stroke();
    const ang = Math.atan2(pyNext-pyCur, pxNext-pxCur);
    const ah = 8;
    ctx.beginPath(); ctx.moveTo(pxNext,pyNext);
    ctx.lineTo(pxNext - ah*Math.cos(ang-0.4), pyNext - ah*Math.sin(ang-0.4));
    ctx.lineTo(pxNext - ah*Math.cos(ang+0.4), pyNext - ah*Math.sin(ang+0.4));
    ctx.closePath(); ctx.fill(); ctx.restore();
    ctx.beginPath(); ctx.fillStyle='#68e0ff'; ctx.arc(pxNext,pyNext,6,0,Math.PI*2); ctx.fill();
  }

  // state
  let state = {x:parseFloat(startEl.value), it:0, maxIt:parseInt(itersEl.value), running:false};

  function render(){
    drawAxes();drawFunction();
    drawPoint(state.x,'#27f58c',9);
    try{
      const eta = parseFloat(lrEl.value);
      const grad = df(state.x);
      const xNext = state.x - eta*grad;
      drawTangentAt(state.x,'#ffb86b');
      drawNextPoint(xNext);
    }catch(e){}
  }

  function updateMath(){
    const eta = parseFloat(lrEl.value);
    const grad = df(state.x);
    const delta = eta * grad;
    mathBox.textContent = `f(x)=x^2+5, ∇f(x)=2x\n∇f(x_t) = ${grad.toFixed(3)}\nΔ=η∇f = ${delta.toFixed(3)}\n x_{t+1} = ${state.x.toFixed(3)} - ${delta.toFixed(3)} = ${(state.x - delta).toFixed(3)}`;
  }

  function logIteration(){
    const row = document.createElement('div');
    const grad = df(state.x); const eta = parseFloat(lrEl.value);
    row.textContent = `${state.it}\t ${state.x.toFixed(3)}\t ${grad.toFixed(3)}\t ${(-eta*grad).toFixed(3)}\t ${f(state.x).toFixed(3)}`;
    iterLog.prepend(row);
  }

  function step(){
    if(state.it >= state.maxIt) return; state.it++;
    logIteration();
    const eta = parseFloat(lrEl.value);
    const grad = df(state.x);
    state.x = state.x - eta * grad;
    updateMath(); render();
  }

  function reset(){
    state.x = parseFloat(startEl.value); state.it=0; state.maxIt=parseInt(itersEl.value); iterLog.innerHTML=''; updateMath(); render();
  }

  // events
  lrEl.addEventListener('input',()=>{lrVal.textContent = lrEl.value; updateMath();});
  startEl.addEventListener('input',()=>{startVal.textContent = startEl.value;});
  itersEl.addEventListener('input',()=>{itersVal.textContent = itersEl.value; state.maxIt = parseInt(itersEl.value);});
  speedEl.addEventListener('input',()=>{speedVal.textContent = speedEl.value + 'x'});
  playBtn.addEventListener('click',()=>{
    if(state.running){state.running=false;playBtn.textContent='Play';return}
    state.running=true;playBtn.textContent='Pause';
    const run = ()=>{
      if(!state.running) return; if(state.it>=state.maxIt){state.running=false;playBtn.textContent='Play';return}
      step();
      setTimeout(run, 600 / parseFloat(speedEl.value));
    };
    run();
  });
  stepBtn.addEventListener('click',step);
  resetBtn.addEventListener('click',reset);

  // initial
  updateMath();render();
})();
