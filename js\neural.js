(function(){
  const container = document.getElementById('nn-canvas');
  const log = document.getElementById('nn-log');
  const mathBox = document.getElementById('nn-math');
  const hiddenEl = document.getElementById('hidden');
  const hiddenVal = document.getElementById('hidden-val');
  const actEl = document.getElementById('act');
  const runBtn = document.getElementById('fn-run');
  const randBtn = document.getElementById('fn-rand');
  const stepFwd = document.getElementById('step-forward');
  const stepBack = document.getElementById('step-back');
  const resetBtn = document.getElementById('reset-nn');

  const canvas = document.createElement('canvas');
  canvas.width = container.clientWidth - 20 || 700; canvas.height = 520; container.appendChild(canvas);
  const ctx = canvas.getContext('2d');

  function randMatrix(r,c){ const m=[]; for(let i=0;i<r;i++){ m.push(Array.from({length:c},()=>Math.random()*2-1)); } return m; }
  function matVecMul(mat,vec){ return mat.map(row=>row.reduce((s,v,i)=>s+v*vec[i],0)); }

  let H = parseInt(hiddenEl.value);
  let W1 = randMatrix(H,2); let b1 = Array.from({length:H},()=>0);
  let W2 = randMatrix(1,H); let b2=[0];

  // visual animation state for smooth transitions (width/alpha per connection)
  function makeVisualState(){
    return {
      // W1: H x 2
      W1: Array.from({length:W1.length},(_,i)=> Array.from({length:2},(_,j)=>({curr:{w:1,a:0.25}, target:{w:1,a:0.25}}))),
      // W2: 1 x H
      W2: Array.from({length:W2[0].length},()=>({curr:{w:1,a:0.25}, target:{w:1,a:0.25}})),
    };
  }

  let visualState = makeVisualState();

  let state = {x:[0,0], h:null, a:null, out:null, step:0};

  function activateArr(arr,fn){ if(fn==='tanh') return arr.map(Math.tanh); if(fn==='relu') return arr.map(x=>Math.max(0,x)); if(fn==='sigmoid') return arr.map(x=>1/(1+Math.exp(-x))); return arr; }

  function forward(x){
    const h = matVecMul(W1,x).map((v,i)=>v + b1[i]);
    const a = activateArr(h, actEl.value);
    const out = matVecMul(W2,a).map((v,i)=>v + b2[i]);
    return {h,a,out};
  }

  function renderCanvas(){
    ctx.clearRect(0,0,canvas.width,canvas.height);
    ctx.fillStyle='#06161e'; ctx.fillRect(0,0,canvas.width,canvas.height);
    // layout: input col, hidden col, output col
    const cols = [100, canvas.width/2, canvas.width-100];
    const inY = [canvas.height/2 - 30, canvas.height/2 + 30];
    // draw inputs
    ctx.fillStyle='#9be3ff'; ctx.strokeStyle='#08333f'; ctx.lineWidth=2;
    inY.forEach((y,i)=>{ ctx.beginPath(); ctx.arc(cols[0], y, 26, 0, Math.PI*2); ctx.fill(); ctx.stroke(); ctx.fillStyle='#00171b'; ctx.font='14px monospace'; ctx.fillText(state.x[i].toFixed(2), cols[0]-10, y+5); ctx.fillStyle='#9be3ff'; });
    // hidden
    const hCount = W1.length;
    const gap = canvas.height / (hCount+1);
    for(let i=0;i<hCount;i++){
      const y = gap*(i+1);
      ctx.fillStyle='#7af59a'; ctx.beginPath(); ctx.arc(cols[1], y, 26,0,Math.PI*2); ctx.fill(); ctx.strokeStyle='#05321f'; ctx.stroke();
      // text value if available
      if(state.a) { ctx.fillStyle='#001b12'; ctx.font='14px monospace'; ctx.fillText(state.a[i].toFixed(3), cols[1]-12, y+5); }
    }
    // output
    ctx.fillStyle='#ffd37a'; ctx.beginPath(); ctx.arc(cols[2], canvas.height/2, 30,0,Math.PI*2); ctx.fill(); ctx.strokeStyle='#3d2b09'; ctx.stroke();
    if(state.out) { ctx.fillStyle='#2a1700'; ctx.font='14px monospace'; ctx.fillText(state.out[0].toFixed(3), cols[2]-12, canvas.height/2+6); }
    // draw connections with color and width based on weights
    // inputs->hidden
  for(let i=0;i<hCount;i++){
      const hy = gap*(i+1);
      for(let j=0;j<2;j++){
        const w = W1[i][j];
    // use animated visual state for width/alpha
    const vs = visualState.W1[i][j].curr;
    const col = w>=0? `rgba(59,130,246,${vs.a})` : `rgba(255,107,107,${vs.a})`;
    const width = vs.w;
    ctx.strokeStyle = col; ctx.lineWidth = width;
        const ySrc = inY[j];
        ctx.beginPath(); ctx.moveTo(cols[0]+26, ySrc); ctx.lineTo(cols[1]-26, hy); ctx.stroke();
        // weight label
        const mx = (cols[0]+26 + cols[1]-26)/2;
        const my = (ySrc + hy)/2;
        ctx.fillStyle = '#cfeefb'; ctx.font='12px monospace'; ctx.fillText(w.toFixed(2), mx-12, my-4);
      }
    }
    // hidden->output
    for(let i=0;i<hCount;i++){
      const hy = gap*(i+1);
      const w = W2[0][i];
  const vs = visualState.W2[i].curr;
  const col = w>=0? `rgba(59,130,246,${vs.a})` : `rgba(255,107,107,${vs.a})`;
  const width = vs.w;
  ctx.strokeStyle = col; ctx.lineWidth = width;
      ctx.beginPath(); ctx.moveTo(cols[1]+26, hy); ctx.lineTo(cols[2]-30, canvas.height/2); ctx.stroke();
      const mx = (cols[1]+26 + cols[2]-30)/2; const my = (hy + canvas.height/2)/2;
      ctx.fillStyle='#cfeefb'; ctx.font='12px monospace'; ctx.fillText(w.toFixed(2), mx-12, my-4);
    }
    ctx.lineWidth = 1; // reset
  }

  function renderStateLog(){
    const lines=[];
    lines.push(`W1: ${JSON.stringify(W1.map(r=>r.map(v=>v.toFixed(2))))}`);
    lines.push(`b1: ${JSON.stringify(b1.map(v=>v.toFixed(2)))}`);
    lines.push(`W2: ${JSON.stringify(W2.map(r=>r.map(v=>v.toFixed(2))))}`);
    if(state.h) lines.push(`h: ${state.h.map(v=>v.toFixed(3))}`);
    if(state.a) lines.push(`a: ${state.a.map(v=>v.toFixed(3))}`);
    if(state.out) lines.push(`out: ${state.out.map(v=>v.toFixed(3))}`);
    log.textContent = lines.join('\n');
  }

  // simple easing and lerp helpers
  function lerp(a,b,t){ return a + (b-a)*t; }
  function easeOutCubic(t){ return 1 - Math.pow(1-t,3); }

  // update visualState targets based on current W1/W2 values
  function refreshVisualTargets(){
    // W1
    for(let i=0;i<W1.length;i++){
      for(let j=0;j<2;j++){
        const w = W1[i][j];
        const mag = Math.min(1, Math.abs(w));
        const targetA = 0.25 + 0.75*mag;
        const targetW = 1 + 4*mag;
        visualState.W1[i][j].target.a = targetA;
        visualState.W1[i][j].target.w = targetW;
      }
    }
    // W2
    for(let i=0;i<W2[0].length;i++){
      const w = W2[0][i];
      const mag = Math.min(1, Math.abs(w));
      visualState.W2[i].target.a = 0.25 + 0.75*mag;
      visualState.W2[i].target.w = 1 + 4*mag;
    }
  }

  // RAF loop to animate visualState and redraw canvas
  let lastTs = null;
  function rafLoop(ts){
    if(!lastTs) lastTs = ts;
    const dt = Math.min(32, ts - lastTs);
    lastTs = ts;
    // animate towards targets
    const t = easeOutCubic(Math.min(1, dt/100));
    for(let i=0;i<visualState.W1.length;i++){
      for(let j=0;j<2;j++){
        const obj = visualState.W1[i][j];
        obj.curr.a = lerp(obj.curr.a, obj.target.a, t);
        obj.curr.w = lerp(obj.curr.w, obj.target.w, t);
      }
    }
    for(let i=0;i<visualState.W2.length;i++){
      const obj = visualState.W2[i];
      obj.curr.a = lerp(obj.curr.a, obj.target.a, t);
      obj.curr.w = lerp(obj.curr.w, obj.target.w, t);
    }
    renderCanvas();
    requestAnimationFrame(rafLoop);
  }


  function showForward(x){ state.x = x; const res = forward(x); state.h = res.h; state.a = res.a; state.out = res.out; renderCanvas(); renderStateLog();
    // formatted math box
    let txt = '';
    txt += '--- Forward pass ---\n';
    for(let i=0;i<state.h.length;i++){
      txt += `h_${i} = ` + W1[i].map((w,j)=>`${w.toFixed(3)}*x_${j}`).join(' + ') + ` + ${b1[i].toFixed(3)} = ${state.h[i].toFixed(6)}\n`;
      txt += `a_${i} = ${actEl.value}(h_${i}) = ${state.a[i].toFixed(6)}\n`;
    }
    txt += `\nOutput:\n y = ` + W2[0].map((w,k)=>`${w.toFixed(3)}*a_${k}`).join(' + ') + ` + ${b2[0].toFixed(3)} = ${state.out[0].toFixed(6)}\n`;
    mathBox.textContent = txt;
  }

  function backpropStep(){
    if(!state.out) return;
    // assume simple MSE with target 0 for demo; compute dL/dy = 2*(y - t)
    const y = state.out[0]; const t = 0; const dLdy = 2*(y - t);
    // gradients for W2: dL/dW2 = dL/dy * a
    const dW2 = W2[0].map((_,k)=> dLdy * state.a[k]);
    const db2 = dLdy;
    // backprop to hidden: dL/da = dL/dy * W2
    const dLa = W2[0].map(w=> dLdy * w);
    // dL/dh = dL/da * a' ; a' depends on activation
    const dha = state.h.map((h,i)=>{
      const act = actEl.value;
      if(act==='tanh') return 1 - Math.tanh(h)**2;
      if(act==='relu') return h>0?1:0;
      if(act==='sigmoid'){ const s = 1/(1+Math.exp(-h)); return s*(1-s); }
      return 1;
    });
    const dLdh = dLa.map((v,i)=> v * dha[i]);
    // dL/dW1 = dL/dh * x
    const dW1 = dLdh.map((g,i)=> [g*state.x[0], g*state.x[1]]);

    // show math in panel
  let txt = '';
  txt += '--- Backprop (single step) ---\n';
  txt += `Loss L=(y-t)^2 with t=0\n`;
  txt += `dL/dy = 2*(y-t) = ${dLdy.toFixed(6)}\n\n`;
  txt += 'Gradients for output layer:\n';
  dW2.forEach((v,k)=> txt += ` dL/dW2[0][${k}] = ${v.toFixed(6)}\n`);
  txt += ` dL/db2 = ${db2.toFixed(6)}\n\n`;
  txt += 'Backprop to hidden:\n';
  dLa.forEach((v,k)=> txt += ` dL/da[${k}] = ${v.toFixed(6)}\n`);
  txt += ` a' (per hidden) = [ ${dha.map(v=>v.toFixed(6)).join(', ')} ]\n`;
  dLdh.forEach((v,k)=> txt += ` dL/dh[${k}] = ${v.toFixed(6)}\n`);
  txt += '\nGradients for W1:\n';
  dW1.forEach((row,i)=> txt += ` dL/dW1[${i}] = [ ${row.map(v=>v.toFixed(6)).join(', ')} ]\n`);
  mathBox.textContent = txt;

    // apply small gradient step for demo
    const lr = 0.1;
    for(let k=0;k<W2[0].length;k++) W2[0][k] -= lr * dW2[k];
    b2[0] -= lr * db2;
    for(let i=0;i<W1.length;i++){ W1[i][0] -= lr * dW1[i][0]; W1[i][1] -= lr * dW1[i][1]; b1[i] -= lr * dLdh[i]; }
  refreshVisualTargets(); renderStateLog();
  }

  hiddenEl.addEventListener('input',()=>{ hiddenVal.textContent = hiddenEl.value; H = parseInt(hiddenEl.value); W1 = randMatrix(H,2); b1 = Array.from({length:H},()=>0); W2 = randMatrix(1,H); b2=[0]; state = {x:[0,0],h:null,a:null,out:null,step:0}; visualState = makeVisualState(); refreshVisualTargets(); renderStateLog(); });
  runBtn.addEventListener('click',()=>{ const x=[Math.random()*2-1, Math.random()*2-1]; showForward(x); });
  randBtn.addEventListener('click',()=>{ W1 = randMatrix(parseInt(hiddenEl.value),2); W2 = randMatrix(1,parseInt(hiddenEl.value)); visualState = makeVisualState(); refreshVisualTargets(); renderStateLog(); });
  stepFwd.addEventListener('click',()=>{ const x=[Math.random()*2-1, Math.random()*2-1]; showForward(x); });
  stepBack.addEventListener('click',()=>{ backpropStep(); });
  resetBtn.addEventListener('click',()=>{ W1 = randMatrix(parseInt(hiddenEl.value),2); b1 = Array.from({length:parseInt(hiddenEl.value)},()=>0); W2 = randMatrix(1,parseInt(hiddenEl.value)); b2=[0]; state={x:[0,0],h:null,a:null,out:null}; mathBox.textContent=''; log.textContent=''; visualState = makeVisualState(); refreshVisualTargets(); renderStateLog(); });

  // initialize visual targets and start RAF loop
  refreshVisualTargets();
  requestAnimationFrame(rafLoop);
  renderStateLog();
})();
