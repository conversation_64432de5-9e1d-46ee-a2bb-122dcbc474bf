<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Neuron & Backpropagation — Interactive Visual</title>
<style>
  :root{
    --bg:#0f172a;        /* slate-900 */
    --panel:#111827;     /* gray-900 */
    --card:#1f2937;      /* gray-800 */
    --muted:#94a3b8;     /* slate-400 */
    --text:#e5e7eb;      /* gray-200 */
    --accent:#22d3ee;    /* cyan-400 */
    --accent2:#f472b6;   /* pink-400 */
    --good:#34d399;      /* green-400 */
    --bad:#f87171;       /* red-400 */
    --warn:#fbbf24;      /* amber-400 */
  }
  *{box-sizing:border-box}
  html,body{height:100%}
  body{
    margin:0; font-family:system-ui,-apple-system,Se<PERSON>e UI,Robot<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON>o <PERSON>,sans-serif;
    background: radial-gradient(1200px 800px at 20% -10%, #1e293b 0%, var(--bg) 60%);
    color:var(--text);
  }
  .container{max-width:1200px; margin:24px auto; padding:0 16px}
  h1{font-weight:800; letter-spacing:.3px; margin:0 0 8px; font-size:clamp(22px,4vw,32px)}
  p.sub{margin:0 0 20px; color:var(--muted)}
  .grid{display:grid; grid-template-columns:1.2fr .8fr; gap:18px}
  @media (max-width: 980px){ .grid{grid-template-columns:1fr;}}

  .card{background:linear-gradient(180deg, #0b1020 0%, var(--card) 100%); border:1px solid #243047; border-radius:18px; box-shadow:0 10px 30px #0005; padding:14px 14px 10px}
  .toolbar{display:flex; gap:8px; flex-wrap:wrap; align-items:center; margin:8px 0 2px}
  .toolbar .sep{width:1px; height:28px; background:#334155; margin:0 2px}
  button{cursor:pointer; border:1px solid #334155; background:#0b1222; color:var(--text); padding:8px 12px; border-radius:12px; font-weight:600}
  button:hover{border-color:#4b5563}
  button.primary{background:linear-gradient(180deg, #0ea5e9 0%, #06b6d4 100%); color:#001018; border:none}
  button.warn{background:linear-gradient(180deg, #fb7185, #f43f5e); color:#1e0b0b; border:none}
  button.ghost{background:transparent}
  button:disabled{opacity:.55; cursor:not-allowed}

  .ctrls{display:grid; grid-template-columns:1fr 1fr; gap:10px}
  .ctrl{background:rgba(15,23,42,.5); border:1px solid #22314b; border-radius:14px; padding:10px}
  .ctrl label{display:flex; justify-content:space-between; font-size:12px; color:var(--muted); margin-bottom:6px}
  .ctrl input[type="range"]{width:100%}
  .row{display:flex; gap:10px; align-items:center; justify-content:space-between}
  .stat{background:rgba(2,6,23,.6); border:1px solid #1e293b; padding:8px 10px; border-radius:12px; font-variant-numeric:tabular-nums; min-width:90px; text-align:center}
  .stat .k{display:block; color:var(--muted); font-size:11px}
  .hint{color:var(--muted); font-size:12px}

  .svg-wrap{position:relative;}
  svg{width:100%; height:520px; display:block}
  .tag{font-size:12px; fill:#a5b4fc}
  .value{font-size:12px; fill:#e2e8f0}
  .grad{font-size:11px; fill:#fde68a}
  .eq{font-family: ui-monospace, SFMono-Regular, Menlo, Consolas, "Liberation Mono", monospace; background:#0b1328; border:1px solid #22314b; border-radius:14px; padding:10px; color:#cbd5e1; overflow:auto}

  canvas#lossChart{width:100%; height:180px; background:#0a1225; border:1px solid #22314b; border-radius:12px}

  .pulse{filter: drop-shadow(0 0 10px var(--accent));}
  .fade{opacity:.25}
</style>
</head>
<body>
  <div class="container">
    <h1>Single Neuron — Forward & Backpropagation (Interactive)</h1>
    <p class="sub">A visual, step‑by‑step simulator of one neuron with sigmoid activation and MSE loss. Change inputs/target, run forward pass, compute gradients, and update weights — all values are drawn <em>on the graph</em>.</p>

    <div class="grid">
      <!-- LEFT: Graph + controls -->
      <section class="card">
        <div class="toolbar">
          <button class="primary" id="btnForward">1) Forward</button>
          <button class="primary" id="btnBackprop">2) Backprop</button>
          <button class="primary" id="btnUpdate">3) Update</button>
          <span class="sep"></span>
          <button id="btnEpoch">Train 1 Epoch</button>
          <button id="btnReset" class="warn">Reset</button>
          <span style="flex:1"></span>
          <span class="hint">Activation: σ(z), Loss: ½(y−t)²</span>
        </div>

        <div class="svg-wrap">
          <!-- SVG graph (positions are logical; scaled to fit) -->
          <svg id="graph" viewBox="0 0 900 520" aria-label="neuron graph">
            <!-- Connections -->
            <defs>
              <marker id="arrow" viewBox="0 0 10 10" refX="10" refY="5" markerWidth="8" markerHeight="8" orient="auto-start-reverse">
                <path d="M 0 0 L 10 5 L 0 10 z" fill="#64748b"/>
              </marker>
              <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                <feGaussianBlur stdDeviation="2.5" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>

            <!-- Left: inputs -->
            <g id="inputs">
              <circle cx="100" cy="120" r="26" fill="#0b1328" stroke="#334155"/>
              <text x="100" y="115" text-anchor="middle" class="tag">x₁</text>
              <text id="x1v" x="100" y="135" text-anchor="middle" class="value">0.50</text>

              <circle cx="100" cy="240" r="26" fill="#0b1328" stroke="#334155"/>
              <text x="100" y="235" text-anchor="middle" class="tag">x₂</text>
              <text id="x2v" x="100" y="255" text-anchor="middle" class="value">0.50</text>

              <circle cx="100" cy="360" r="26" fill="#0b1328" stroke="#334155"/>
              <text x="100" y="355" text-anchor="middle" class="tag">bias</text>
              <text id="bv" x="100" y="375" text-anchor="middle" class="value">1</text>
            </g>

            <!-- Weights on edges -->
            <g id="edges" stroke="#64748b" stroke-width="2" marker-end="url(#arrow)">
              <line id="e1" x1="126" y1="120" x2="370" y2="180" />
              <line id="e2" x1="126" y1="240" x2="370" y2="200" />
              <line id="eb" x1="126" y1="360" x2="370" y2="220" />
            </g>

            <!-- Edge labels: weights & gradients -->
            <g id="wlabels" font-variant-numeric="tabular-nums">
              <text id="w1label" x="240" y="130" class="value">w₁ = 0.50</text>
              <text id="gw1label" x="240" y="150" class="grad">∂L/∂w₁ = 0.00</text>

              <text id="w2label" x="240" y="230" class="value">w₂ = -0.30</text>
              <text id="gw2label" x="240" y="250" class="grad">∂L/∂w₂ = 0.00</text>

              <text id="blabel" x="240" y="330" class="value">b = 0.10</text>
              <text id="gblabel" x="240" y="350" class="grad">∂L/∂b = 0.00</text>
            </g>

            <!-- Neuron (linear + activation) -->
            <g id="neuron">
              <circle cx="420" cy="200" r="40" fill="#0ea5e9" opacity="0.18" />
              <circle cx="420" cy="200" r="36" fill="#0b1328" stroke="#22d3ee" stroke-width="2" />
              <text x="420" y="196" text-anchor="middle" class="tag">z = w·x + b</text>
              <text id="zval" x="420" y="216" text-anchor="middle" class="value">z = 0.00</text>
            </g>

            <!-- Activation output y -->
            <g id="act">
              <line x1="460" y1="200" x2="620" y2="200" stroke="#64748b" stroke-width="2" marker-end="url(#arrow)"/>
              <circle cx="660" cy="200" r="34" fill="#0b1328" stroke="#334155" />
              <text x="660" y="195" text-anchor="middle" class="tag">y = σ(z)</text>
              <text id="yval" x="660" y="215" text-anchor="middle" class="value">y = 0.00</text>
            </g>

            <!-- Target & loss -->
            <g id="target">
              <circle cx="660" cy="90" r="24" fill="#0b1328" stroke="#334155"/>
              <text x="660" y="86" text-anchor="middle" class="tag">t</text>
              <text id="tval" x="660" y="104" text-anchor="middle" class="value">0.80</text>

              <line x1="660" y1="124" x2="660" y2="166" stroke="#64748b" stroke-width="2" marker-end="url(#arrow)" />
              <text x="676" y="152" class="tag">compare</text>

              <rect x="720" y="160" width="150" height="86" rx="12" fill="#0b1328" stroke="#334155"/>
              <text x="795" y="178" text-anchor="middle" class="tag">Loss L</text>
              <text id="lossval" x="795" y="198" text-anchor="middle" class="value">0.000</text>
              <text id="dydzval" x="795" y="216" text-anchor="middle" class="grad">∂y/∂z = 0.000</text>
              <text id="dldzval" x="795" y="232" text-anchor="middle" class="grad">∂L/∂z = 0.000</text>
            </g>

            <!-- Output gradient fan-out back to weights (visual arrows) -->
            <g id="backArrows" stroke="#f59e0b" stroke-width="2" marker-end="url(#arrow)" opacity="0.9">
              <line id="b1" x1="620" y1="200" x2="460" y2="200" />
              <line id="b2" x1="370" y1="180" x2="126" y2="120" />
              <line id="b3" x1="370" y1="200" x2="126" y2="240" />
              <line id="b4" x1="370" y1="220" x2="126" y2="360" />
            </g>

          </svg>
        </div>

        <div class="toolbar" style="margin-top:8px">
          <div class="row" style="gap:12px; flex-wrap:wrap">
            <div class="stat"><span class="k">Learning rate η</span><span id="lrStat">0.10</span></div>
            <div class="stat"><span class="k">z</span><span id="zStat">0.0000</span></div>
            <div class="stat"><span class="k">y</span><span id="yStat">0.0000</span></div>
            <div class="stat"><span class="k">Loss</span><span id="lossStat">0.0000</span></div>
            <div class="stat"><span class="k">Step</span><span id="stepStat">0</span></div>
          </div>
        </div>

        <div class="ctrls" style="margin-top:8px">
          <div class="ctrl">
            <label>Input x₁ <span id="x1Lbl">0.50</span></label>
            <input type="range" id="x1" min="-1" max="1" step="0.01" value="0.50" />
            <label>Input x₂ <span id="x2Lbl">0.50</span></label>
            <input type="range" id="x2" min="-1" max="1" step="0.01" value="0.50" />
            <label>Target t <span id="tLbl">0.80</span></label>
            <input type="range" id="t" min="0" max="1" step="0.01" value="0.80" />
          </div>
          <div class="ctrl">
            <label>Weight w₁ <span id="w1Lbl">0.50</span></label>
            <input type="range" id="w1" min="-2" max="2" step="0.01" value="0.50" />
            <label>Weight w₂ <span id="w2Lbl">-0.30</span></label>
            <input type="range" id="w2" min="-2" max="2" step="0.01" value="-0.30" />
            <label>Bias b <span id="bLbl">0.10</span></label>
            <input type="range" id="b" min="-2" max="2" step="0.01" value="0.10" />
            <label>Learning rate η <span id="lrLbl">0.10</span></label>
            <input type="range" id="lr" min="0.001" max="1" step="0.001" value="0.10" />
          </div>
        </div>

        <details style="margin-top:10px">
          <summary>Show equations</summary>
          <div class="eq" style="margin-top:8px">
            <div>z = w₁x₁ + w₂x₂ + b</div>
            <div>y = σ(z) = 1/(1 + e^{-z})</div>
            <div>L = ½ (y − t)²</div>
            <div style="margin-top:6px">∂L/∂y = (y − t), &nbsp; ∂y/∂z = y(1−y), &nbsp; ∂L/∂z = ∂L/∂y · ∂y/∂z</div>
            <div>∂L/∂wᵢ = ∂L/∂z · xᵢ, &nbsp; ∂L/∂b = ∂L/∂z · 1</div>
            <div>Update: wᵢ ← wᵢ − η · ∂L/∂wᵢ, &nbsp; b ← b − η · ∂L/∂b</div>
          </div>
        </details>
      </section>

      <!-- RIGHT: Mini monitor + loss chart + dataset trainer -->
      <section class="card">
        <h3 style="margin:6px 4px 10px">Trainer & Monitor</h3>
        <div class="ctrl" style="margin-bottom:10px">
          <label>Quick dataset (cycles through on “Train 1 Epoch”)</label>
          <div class="row" style="gap:8px; flex-wrap:wrap">
            <button class="ghost" data-preset="0.2,0.1,1">(x₁=0.2, x₂=0.1, t=1)</button>
            <button class="ghost" data-preset="0.9,0.7,1">(0.9, 0.7, 1)</button>
            <button class="ghost" data-preset="-0.6,0.3,0">(-0.6, 0.3, 0)</button>
            <button class="ghost" data-preset="0.1,-0.8,0">(0.1, -0.8, 0)</button>
          </div>
        </div>

        <div class="row" style="gap:10px">
          <div class="stat"><span class="k">∂L/∂w₁</span><span id="gW1">0.0000</span></div>
          <div class="stat"><span class="k">∂L/∂w₂</span><span id="gW2">0.0000</span></div>
          <div class="stat"><span class="k">∂L/∂b</span><span id="gB">0.0000</span></div>
        </div>

        <div style="margin:10px 0">
          <canvas id="lossChart" width="520" height="180" aria-label="loss chart"></canvas>
        </div>

        <div class="eq">
          <strong style="color:#93c5fd">Tips:</strong>
          <ul style="margin:8px 0 0 18px; padding:0 0 0 6px">
            <li>Click <em>Forward</em> to push values through the network (blue arrows glow).</li>
            <li>Click <em>Backprop</em> to compute gradients (amber arrows glow). Gradients are drawn near weights.</li>
            <li>Click <em>Update</em> to apply gradient descent with learning rate η.</li>
            <li>Use <em>Train 1 Epoch</em> to iterate the 4-sample dataset once (Forward→Backprop→Update per sample).</li>
            <li>You can drag weights and inputs to see how everything changes immediately.</li>
          </ul>
        </div>
      </section>
    </div>
  </div>

<script>
// ===== Utility =====
const $ = sel => document.querySelector(sel);
const fmt = (x, n=4) => Number(x).toFixed(n);

// ===== State =====
const state = {
  x1: 0.5, x2: 0.5, t: 0.8,
  w1: 0.5, w2: -0.3, b: 0.1,
  lr: 0.10,
  step: 0,
  last: { z: 0, y: 0, L: 0, dydz: 0, dldz: 0, g: {w1:0,w2:0,b:0} },
  history: [],
  dataset: [ {x1:.2,x2:.1,t:1}, {x1:.9,x2:.7,t:1}, {x1:-.6,x2:.3,t:0}, {x1:.1,x2:-.8,t:0} ],
  dsi: 0,
};

// ===== Math =====
const sigmoid = z => 1/(1+Math.exp(-z));
const forward = (s) => {
  const z = s.w1*s.x1 + s.w2*s.x2 + s.b;
  const y = sigmoid(z);
  const L = 0.5 * (y - s.t) ** 2;
  const dydz = y*(1-y);
  return {z,y,L,dydz};
};
const backprop = (s, cache) => {
  const dLdy = (cache.y - s.t);
  const dLdz = dLdy * cache.dydz;
  const g = {
    w1: dLdz * s.x1,
    w2: dLdz * s.x2,
    b: dLdz * 1
  };
  return {dLdy, dLdz, g};
};

// ===== UI bindings =====
const sliders = {
  x1: $('#x1'), x2: $('#x2'), t: $('#t'),
  w1: $('#w1'), w2: $('#w2'), b: $('#b'), lr: $('#lr')
};
const labels = {
  x1: $('#x1Lbl'), x2: $('#x2Lbl'), t: $('#tLbl'),
  w1: $('#w1Lbl'), w2: $('#w2Lbl'), b: $('#bLbl'), lr: $('#lrLbl')
};
function syncFromSliders(){
  for(const k of Object.keys(sliders)) state[k] = Number(sliders[k].value);
  drawAll();
}
for(const k of Object.keys(sliders)){
  sliders[k].addEventListener('input', ()=>{ labels[k].textContent = fmt(sliders[k].value,2); syncFromSliders(); });
}

// ===== Drawing =====
const ctxLoss = $('#lossChart').getContext('2d');
function drawLoss(){
  const W = ctxLoss.canvas.width, H = ctxLoss.canvas.height;
  ctxLoss.clearRect(0,0,W,H);
  // frame
  ctxLoss.strokeStyle = '#334155'; ctxLoss.lineWidth = 1; ctxLoss.strokeRect(0.5,0.5,W-1,H-1);
  const n = state.history.length; if(!n){return}
  const maxL = Math.max(...state.history.map(h=>h.L), 0.001);
  const pad = 8; const xstep = (W-2*pad)/Math.max(1,n-1);
  ctxLoss.beginPath(); ctxLoss.lineWidth = 2; ctxLoss.strokeStyle = '#22d3ee';
  for(let i=0;i<n;i++){
    const x = pad + i*xstep; const y = H - pad - (state.history[i].L/maxL)*(H-2*pad);
    if(i===0) ctxLoss.moveTo(x,y); else ctxLoss.lineTo(x,y);
  }
  ctxLoss.stroke();
}

function glow(selector, on){
  const el = $(selector); if(!el) return;
  if(on) el.classList.add('pulse'); else el.classList.remove('pulse');
}
function fade(selector, on){
  const el = $(selector); if(!el) return;
  if(on) el.classList.add('fade'); else el.classList.remove('fade');
}

function drawAll(){
  // Update node text values
  $('#x1v').textContent = fmt(state.x1,2);
  $('#x2v').textContent = fmt(state.x2,2);
  $('#bv').textContent  = '1';
  $('#tval').textContent = fmt(state.t,2);

  $('#w1label').textContent = `w₁ = ${fmt(state.w1,2)}`;
  $('#w2label').textContent = `w₂ = ${fmt(state.w2,2)}`;
  $('#blabel').textContent  = `b = ${fmt(state.b,2)}`;

  $('#w1Lbl').textContent = fmt(state.w1,2);
  $('#w2Lbl').textContent = fmt(state.w2,2);
  $('#bLbl').textContent  = fmt(state.b,2);
  $('#lrLbl').textContent = fmt(state.lr,2);
  $('#lrStat').textContent= fmt(state.lr,2);

  const f = forward(state);
  $('#zval').textContent = `z = ${fmt(f.z)}`;
  $('#yval').textContent = `y = ${fmt(f.y)}`;
  $('#lossval').textContent = fmt(f.L,3);
  $('#zStat').textContent = fmt(f.z);
  $('#yStat').textContent = fmt(f.y);
  $('#lossStat').textContent = fmt(f.L);

  // If last gradients exist, show them
  $('#dydzval').textContent = `∂y/∂z = ${fmt(f.dydz)}`;
  $('#dldzval').textContent = `∂L/∂z = ${fmt(state.last.dldz || 0)}`;
  $('#gw1label').textContent = `∂L/∂w₁ = ${fmt(state.last.g.w1 || 0)}`;
  $('#gw2label').textContent = `∂L/∂w₂ = ${fmt(state.last.g.w2 || 0)}`;
  $('#gblabel').textContent  = `∂L/∂b = ${fmt(state.last.g.b || 0)}`;
  $('#gW1').textContent = fmt(state.last.g.w1 || 0);
  $('#gW2').textContent = fmt(state.last.g.w2 || 0);
  $('#gB').textContent  = fmt(state.last.g.b || 0);
  $('#stepStat').textContent = state.step;

  // Update labels next to sliders for inputs/target
  $('#x1Lbl').textContent = fmt(state.x1,2);
  $('#x2Lbl').textContent = fmt(state.x2,2);
  $('#tLbl').textContent  = fmt(state.t,2);
  $('#x1v').textContent   = fmt(state.x1,2);
  $('#x2v').textContent   = fmt(state.x2,2);
  $('#tval').textContent  = fmt(state.t,2);

  drawLoss();
}

// ===== Actions =====
function doForward(){
  const f = forward(state);
  state.last = { ...state.last, z:f.z, y:f.y, L:f.L, dydz:f.dydz };
  // visual: highlight forward arrows
  glow('#edges', true); glow('#act', true); fade('#backArrows', true);
  setTimeout(()=>{ glow('#edges', false); glow('#act', false); fade('#backArrows', false); }, 500);
  state.history.push({ step: state.step, L: f.L });
  drawAll();
}

function doBackprop(){
  const f = forward(state);
  const bp = backprop(state, f);
  state.last = { ...state.last, dldz: bp.dLdz, g: bp.g };
  // visual: highlight back arrows
  glow('#backArrows', true); fade('#edges', true);
  setTimeout(()=>{ glow('#backArrows', false); fade('#edges', false); }, 600);
  drawAll();
}

function doUpdate(){
  // if gradients not computed yet, compute once
  if(state.last.g==null || Number.isNaN(state.last.g.w1)) doBackprop();
  const {w1,w2,b,lr} = state;
  const g = state.last.g;
  state.w1 = w1 - lr * g.w1;
  state.w2 = w2 - lr * g.w2;
  state.b  = b  - lr * g.b;
  state.step++;
  sliders.w1.value = state.w1;
  sliders.w2.value = state.w2;
  sliders.b.value  = state.b;
  labels.w1.textContent = fmt(state.w1,2);
  labels.w2.textContent = fmt(state.w2,2);
  labels.b.textContent  = fmt(state.b,2);
  // quick pulse on weights
  const wl = ['#w1label','#w2label','#blabel']; wl.forEach(sel=>glow(sel,true));
  setTimeout(()=>wl.forEach(sel=>glow(sel,false)), 500);
  drawAll();
}

function doEpoch(){
  const order = [0,1,2,3];
  for(const idx of order){
    const sample = state.dataset[idx];
    state.x1 = sample.x1; state.x2 = sample.x2; state.t = sample.t;
    sliders.x1.value = sample.x1; sliders.x2.value = sample.x2; sliders.t.value = sample.t;
    doForward(); doBackprop(); doUpdate();
  }
}

function doReset(){
  state.x1=0.5; state.x2=0.5; state.t=0.8; state.w1=0.5; state.w2=-0.3; state.b=0.1; state.lr=0.1;
  state.history=[]; state.step=0; state.last={ z:0,y:0,L:0,dydz:0,dldz:0,g:{w1:0,w2:0,b:0} };
  for(const k of ['x1','x2','t','w1','w2','b','lr']) sliders[k].value = state[k];
  drawAll();
}

// Bind buttons
$('#btnForward').addEventListener('click', doForward);
$('#btnBackprop').addEventListener('click', doBackprop);
$('#btnUpdate').addEventListener('click', doUpdate);
$('#btnEpoch').addEventListener('click', doEpoch);
$('#btnReset').addEventListener('click', doReset);

// Dataset presets
for(const btn of document.querySelectorAll('[data-preset]')){
  btn.addEventListener('click', ()=>{
    const [x1,x2,t] = btn.dataset.preset.split(',').map(Number);
    state.x1=x1; state.x2=x2; state.t=t;
    sliders.x1.value=x1; sliders.x2.value=x2; sliders.t.value=t;
    drawAll();
  });
}

// Init
syncFromSliders(); doForward();
</script>
</body>
</html>
