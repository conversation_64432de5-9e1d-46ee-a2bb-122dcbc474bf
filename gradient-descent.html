<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Gradient Descent — ML Visual Playground</title>
  <link rel="stylesheet" href="css/styles.css">
</head>
<body>
  <div class="app-shell">
    <header class="topbar">
      <div class="brand">ML Visual Playground</div>
      <nav class="nav">
        <a href="index.html" class="btn">Home</a>
        <a href="neural-network.html" class="btn">Neural Network</a>
      </nav>
    </header>

    <main class="content">
      <div class="demo-shell">
        <div class="panel">
          <div class="canvas-area" id="gd-canvas"></div>

          <div style="display:flex;gap:8px;margin-top:12px;align-items:center;">
            <label class="kv"><span class="label">Show:</span></label>
            <label><input type="checkbox" id="show-function" checked> f(x)</label>
            <label><input type="checkbox" id="show-tangent" checked> tangent</label>
            <label><input type="checkbox" id="show-update" checked> update</label>
          </div>

          <div style="margin-top:10px;display:flex;gap:8px;">
            <button id="play" class="btn-action">Play</button>
            <button id="step" class="btn-action">Step</button>
            <button id="reset" class="btn-action">Reset</button>
          </div>
        </div>

        <aside class="panel controls">
          <div class="control">
            <div class="label">Learning rate η: <span id="lr-val">0.2</span></div>
            <input type="range" id="lr" min="0" max="1" step="0.01" value="0.2">
          </div>

          <div class="control">
            <div class="label">Start x0: <span id="start-val">-8.0</span></div>
            <input type="range" id="start" min="-10" max="10" step="0.1" value="-8">
          </div>

          <div class="control">
            <div class="label">Iterations: <span id="iters-val">20</span></div>
            <input type="range" id="iters" min="1" max="200" step="1" value="20">
          </div>

          <div class="control">
            <div class="label">Speed: <span id="speed-val">1x</span></div>
            <input type="range" id="speed" min="0.1" max="3" step="0.1" value="1">
          </div>

          <div class="control">
            <div class="label">Update math (current step t)</div>
            <div class="small-log" id="math"></div>
          </div>

          <div class="control">
            <div class="label">Iterations</div>
            <div class="small-log" id="iter-log"></div>
          </div>
        </aside>
      </div>
    </main>

    <footer class="footer">Interactive demo</footer>
  </div>

  <script src="js/gd.bundle.js"></script>
</body>
</html>
