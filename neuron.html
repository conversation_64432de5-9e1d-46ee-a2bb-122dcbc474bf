<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <title>Single Neuron Simulator</title>
  <link rel="stylesheet" href="css/styles.css">
  <style>
    /* local page tweaks */
    .panel { max-width:1100px; margin:28px auto; display:flex; gap:18px; }
    .left { flex:1; }
    .right { width:360px; }
    .controls label{ display:block; margin:8px 0 4px; font-family:monospace; }
    .controls input[type=range]{ width:100%; }
    .mathbox{ white-space:pre; background:var(--panel); padding:12px; border-radius:8px; font-family:monospace; color:var(--ink); min-height:140px; }
  </style>
</head>
<body>
  <header class="site-header"><h1>Single Neuron Simulator</h1></header>
  <main>
    <div class="panel">
      <div class="left">
        <div id="neuron-viz" style="width:100%; height:420px; background:transparent; border-radius:12px; padding:12px;"></div>
      </div>
      <aside class="right">
        <div class="controls">
          <label>Inputs x1, x2, x3</label>
          <div style="display:grid;grid-template-columns:1fr 80px;gap:8px;align-items:center">
            <input id="input-x1" type="range" min="-2" max="2" step="0.01" value="1">
            <input id="input-x1-num" type="number" step="0.01" value="1" style="width:80px;padding:6px;border-radius:6px;border:1px solid #ddd;">
            <input id="input-x2" type="range" min="-2" max="2" step="0.01" value="0.5">
            <input id="input-x2-num" type="number" step="0.01" value="0.5" style="width:80px;padding:6px;border-radius:6px;border:1px solid #ddd;">
            <input id="input-x3" type="range" min="-2" max="2" step="0.01" value="-0.5">
            <input id="input-x3-num" type="number" step="0.01" value="-0.5" style="width:80px;padding:6px;border-radius:6px;border:1px solid #ddd;">
          </div>
          <div style="display:flex;justify-content:space-between;font-size:12px;color:var(--muted)"><span>-2</span><span>2</span></div>

          <label>Weights w1, w2, w3</label>
          <div style="display:grid;grid-template-columns:1fr 80px;gap:8px;align-items:center">
            <input id="weight-w1" type="range" min="-3" max="3" step="0.01" value="0.8">
            <input id="weight-w1-num" type="number" step="0.01" value="0.8" style="width:80px;padding:6px;border-radius:6px;border:1px solid #ddd;">
            <input id="weight-w2" type="range" min="-3" max="3" step="0.01" value="-0.4">
            <input id="weight-w2-num" type="number" step="0.01" value="-0.4" style="width:80px;padding:6px;border-radius:6px;border:1px solid #ddd;">
            <input id="weight-w3" type="range" min="-3" max="3" step="0.01" value="0.2">
            <input id="weight-w3-num" type="number" step="0.01" value="0.2" style="width:80px;padding:6px;border-radius:6px;border:1px solid #ddd;">
          </div>

          <label>Bias b</label>
          <div style="display:flex;gap:8px;align-items:center">
            <input id="bias-b" type="range" min="-3" max="3" step="0.01" value="0.0" style="flex:1">
            <input id="bias-b-num" type="number" step="0.01" value="0.0" style="width:80px;padding:6px;border-radius:6px;border:1px solid #ddd;margin-left:4px">
          </div>

          <label>Activation</label>
          <select id="activation">
            <option value="linear">linear</option>
            <option value="tanh">tanh</option>
            <option value="relu">relu</option>
            <option value="sigmoid">sigmoid</option>
          </select>

          <div style="margin-top:12px">
            <label>Activation chart</label>
            <div id="activation-chart" style="width:100%;height:140px;border-radius:8px;background:var(--panel);display:flex;align-items:center;justify-content:center;padding:8px"></div>
          </div>

          <label>Learning rate</label>
          <input id="lr" type="range" min="0" max="1" step="0.001" value="0.1">
          <div style="display:flex;gap:8px;margin-top:12px">
            <button id="step-forward">Forward</button>
            <button id="step-back">Backprop</button>
            <button id="rand">Randomize</button>
            <button id="reset">Reset</button>
          </div>

          <label style="margin-top:12px">Target (t)</label>
          <input id="target" type="number" step="0.01" value="0">

        </div>

        <h3 style="margin-top:14px">Math</h3>
        <div class="mathbox" id="math-box">--</div>

      </aside>
    </div>
  </main>
  <script src="js/neuron.js"></script>
</body>
</html>
