<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Interactive CNN Backprop Simulation</title>
  <style>
    :root {
      --bg: #0f172a; /* slate-900 */
      --panel: #111827; /* gray-900 */
      --muted: #334155; /* slate-600 */
      --text: #e5e7eb; /* gray-200 */
      --accent: #22d3ee; /* cyan-400 */
      --accent-2: #a78bfa; /* violet-400 */
      --good: #34d399; /* emerald-400 */
      --bad: #f87171; /* red-400 */
      --shadow: 0 15px 30px rgba(0,0,0,.35);
      --radius: 16px;
    }
    * { box-sizing: border-box; font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, 'Helvetica Neue', <PERSON><PERSON>, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji"; }
    html, body { height: 100%; }
    body { margin: 0; background: radial-gradient(1200px 800px at 20% -10%, #1f2937 0, transparent 60%), var(--bg); color: var(--text); }
    header { padding: 20px 24px; display:flex; align-items:center; justify-content:space-between; gap: 12px; }
    header h1 { margin: 0; font-weight: 800; letter-spacing: .3px; font-size: clamp(18px, 2.2vw, 28px); }
    header .sub { color: #9ca3af; font-weight: 500; font-size: 12px; }

    .app { display: grid; grid-template-columns: 360px 1fr; gap: 18px; padding: 0 24px 24px; }

    .panel { background: linear-gradient(180deg, rgba(255,255,255,.02), rgba(255,255,255,.01)); border: 1px solid #1f2937; border-radius: var(--radius); box-shadow: var(--shadow); padding: 16px; backdrop-filter: blur(2px); }

    /* Controls */
    .controls { display: grid; gap: 12px; position: sticky; top: 12px; height: fit-content; }
    .row { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; }
    .btn { border: 1px solid #334155; background: #0b1220; color: var(--text); padding: 10px 12px; border-radius: 12px; cursor: pointer; font-weight: 700; letter-spacing: .3px; box-shadow: inset 0 -2px 0 rgba(255,255,255,.03); transition: transform .04s ease, border-color .2s ease; }
    .btn.icon { width: 28px; height: 28px; padding: 0; display:inline-flex; align-items:center; justify-content:center; font-weight:900; border-radius: 8px; }
    .btn.icon.small { width: 24px; height: 24px; font-size: 12px; }
    .btn:hover { border-color: #475569; }
    .btn:active { transform: translateY(1px); }
    .btn.primary { background: linear-gradient(135deg, #0ea5e9, #22d3ee); color: #001218; border: none; }
    .btn.warn { background: linear-gradient(135deg, #ef4444, #f43f5e); color: #fff; border: none; }
    .btn.ghost { background: transparent; border-color: #475569; }
    .seg { display:flex; gap:8px; }
    .seg .btn { flex: 1; }

    label { font-size: 12px; color: #a1a1aa; display:block; margin-bottom: 6px; }
    input[type="range"] { width: 100%; }
    .value { font-variant-numeric: tabular-nums; color: var(--accent); margin-left: 6px; }

    /* Grid heatmaps */
    .grids { display: grid; grid-template-columns: repeat(3, minmax(180px, 1fr)); gap: 14px; }
    .grid-panel { padding: 12px; border-radius: 14px; border: 1px solid #1f2937; background: #0b1220; }
    .grid-title { display:flex; align-items:center; justify-content:space-between; font-size: 12px; color: #a3a3a3; margin-bottom: 8px; }
    .grid-title b { color: #e5e7eb; font-weight: 800; letter-spacing: .2px; }
    .grid-title .actions { display:flex; align-items:center; gap:6px; }
    .grid-title .btn.icon.small { line-height: 1; }
    .matrix { display: grid; gap: 3px; }
    .cell { width: 30px; height: 30px; display:flex; align-items:center; justify-content:center; border-radius: 6px; border: 1px solid #1f2937; font-size: 12px; font-weight: 700; color: #0b1220; position: relative; user-select: none; cursor: crosshair; }
    .cell span { position: absolute; inset: 0; border-radius: 6px; }
    .cell small { position: relative; z-index: 2; font-variant-numeric: tabular-nums; color: #111827; text-shadow: 0 1px 0 rgba(255,255,255,.2); }
    .legend { display:flex; gap: 8px; align-items:center; font-size: 11px; color: #94a3b8; margin-top: 6px; }
    .swatch { width: 22px; height: 12px; border-radius: 999px; background: linear-gradient(90deg, #0ea5e9, #22d3ee); border: 1px solid #1f2937; }

    /* Loss chart */
    .chart { height: 160px; width: 100%; border-radius: 12px; border: 1px solid #1f2937; background: #0b1220; position: relative; overflow: hidden; }
    .chart canvas { position:absolute; inset:0; }
    .chart .axis { position:absolute; bottom:0; left:0; right:0; height:1px; background:#1f2937; }

    /* Tooltip */
    .tooltip { position: fixed; z-index: 9999; max-width: 520px; background: #0b1220; color: var(--text); border: 1px solid #1f2937; border-radius: 12px; box-shadow: var(--shadow); padding: 10px 12px; display: none; }
    .tooltip h4 { margin: 0 0 6px 0; font-size: 12px; color: #a3a3a3; font-weight: 600; }
    .tooltip .content { font-size: 12px; line-height: 1.5; }
    .tooltip table { border-collapse: collapse; }
    .tooltip td { border: 1px solid #1f2937; padding: 2px 6px; font-variant-numeric: tabular-nums; }

    footer { padding: 14px 24px 32px; color:#94a3b8; font-size: 12px; text-align:center; }
    footer a { color: var(--accent); text-decoration: none; }

    .hint { color:#9ca3af; font-size: 12px; margin-top: 6px; }
  </style>
</head>
<body>
  <header>
    <div>
      <h1>Interactive CNN: Convolution + ReLU + MSE Loss (with Backprop)</h1>
      <div class="sub">A tiny visual simulator: paint the input, tweak the kernel, and watch gradients update the weights.</div>
    </div>
  </header>

  <div class="app">
    <!-- Control panel -->
    <section class="panel controls">
      <div class="row">
        <button id="btnRun" class="btn primary">▶ Run</button>
        <button id="btnStep" class="btn">Step</button>
      </div>
      <div class="row">
        <button id="btnReset" class="btn ghost">Reset</button>
        <button id="btnRandTarget" class="btn">Randomize Target</button>
      </div>

      <div>
        <label>Learning rate <span id="lrVal" class="value"></span></label>
        <input type="range" id="lr" min="0.001" max="0.5" step="0.001" value="0.1" />
      </div>
      <div>
        <label>Noise in input painting <span id="noiseVal" class="value"></span></label>
        <input type="range" id="noise" min="0" max="0.3" step="0.01" value="0.05" />
      </div>
      <div class="row">
        <div>
          <label>Optimizer</label>
          <div class="seg">
            <button id="optSGD" class="btn">SGD</button>
            <button id="optMomentum" class="btn">Momentum</button>
            <button id="optAdam" class="btn">Adam</button>
          </div>
        </div>
        <div>
          <label>Activation</label>
          <div class="seg">
            <button id="actReLU" class="btn">ReLU</button>
            <button id="actLinear" class="btn">Linear</button>
          </div>
        </div>
      </div>

      <div class="chart">
        <canvas id="loss"></canvas>
        <div class="axis"></div>
      </div>
      <div style="display:flex; align-items:center; gap:8px;">
        <div id="stats" class="hint" style="margin:0;">Steps: 0 | Loss: –</div>
        <button id="btnExplain" class="btn icon small" title="Show step calculations">?</button>
      </div>
      <div class="hint">Show calculations for output position:
        <select id="posSelect"></select>
      </div>
      <div id="stepDetails" class="panel" style="margin-top:8px; padding:12px; display:none;">
        <div style="font-size:12px;color:#9ca3af;margin-bottom:6px;">Step calculations & equations</div>
        <div id="details"></div>
      </div>

      <div id="diag" class="hint"></div>
      <div class="hint">Tip: click & drag on the <b>Input</b> or <b>Kernel</b> grids to paint values. Shift-drag to erase.</div>
    </section>

    <!-- Visualization panel -->
    <section class="panel">
      <div class="grids" id="grids"></div>
    </section>
  </div>

  <footer>
    Single 3×3 filter over a 7×7 input (stride 1, no padding). Loss = MSE(activation, target). Backprop updates the filter. Visuals show values and a blue heatmap where brighter = larger (signed via diverging scale).
  </footer>

  <script>
    // -----------------------
    // Utility helpers
    // -----------------------
    const clamp = (x, a, b) => Math.max(a, Math.min(b, x));
    const randn = () => {
      // Box–Muller
      let u = 0, v = 0;
      while (u === 0) u = Math.random();
      while (v === 0) v = Math.random();
      return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
    };

    // Diverging color map from negative (violet) to positive (cyan)
    function heatColor(v, vmax) {
      const t = vmax === 0 ? 0 : 0.5 + 0.5 * (v / vmax); // map [-vmax, vmax] -> [0,1]
      const c1 = [167, 139, 250]; // violet-400
      const c2 = [34, 211, 238];  // cyan-400
      const c = c1.map((c1i, i) => Math.round(c1i * (1 - t) + c2[i] * t));
      return `rgb(${c[0]},${c[1]},${c[2]})`;
    }

    // -----------------------
    // Model (single conv filter + activation)
    // -----------------------
    const H = 7, W = 7;          // input size
    const K = 3;                 // kernel size
    const OH = H - K + 1;        // output height
    const OW = W - K + 1;        // output width

    // State
    let X = randnMatrix(H, W, 0.1);
    let KER = randnMatrix(K, K, 0.1);
    let target = randnMatrix(OH, OW, 0.2); // target map

    let act = 'linear';
    let opt = 'sgd';
    let lr = 0.1;
    let t = 0; // steps
    let stepCount = 0;
    let lastDeltaW = zeros(K, K);

    // Optimizer buffers
    let v = zeros(K, K);       // momentum
    let m = zeros(K, K);       // adam m
    let u = zeros(K, K);       // adam v

    // -----------------------
    // Math helpers
    // -----------------------
    function zeros(h, w) { return Array.from({length: h}, () => Array(w).fill(0)); }
    function randnMatrix(h, w, scale=1) { return Array.from({length: h}, () => Array.from({length: w}, () => randn()*scale)); }
    function clone(A){ return A.map(r => r.slice()); }

    function conv2d_valid(X, K) {
      const h = X.length, w = X[0].length;
      const kh = K.length, kw = K[0].length;
      const oh = h - kh + 1, ow = w - kw + 1;
      const Y = zeros(oh, ow);
      for (let i = 0; i < oh; i++) {
        for (let j = 0; j < ow; j++) {
          let s = 0;
          for (let a = 0; a < kh; a++) {
            for (let b = 0; b < kw; b++) {
              s += X[i + a][j + b] * K[a][b];
            }
          }
          Y[i][j] = s;
        }
      }
      return Y;
    }

    function relu(M) { return M.map(r => r.map(x => Math.max(0, x))); }
    function reluGrad(M) { return M.map(r => r.map(x => x >= 0 ? 1 : 0)); }

    function mse(A, T) {
      // Expect matching shapes
      if (!Array.isArray(A) || A.length === 0 || !Array.isArray(A[0]) ||
          !Array.isArray(T) || T.length !== A.length || T[0].length !== A[0].length) {
        throw new Error('mse: A and T must be same non-empty shape');
      }
      let s = 0; let n = 0;
      for (let i = 0; i < A.length; i++) for (let j = 0; j < A[0].length; j++) { s += (A[i][j] - T[i][j])**2; n++; }
      return s / n;
    }

    function add(A, B){ return A.map((r,i) => r.map((x,j) => x + B[i][j])); }
    function sub(A, B){ return A.map((r,i) => r.map((x,j) => x - B[i][j])); }
    function mul(A, B){ return A.map((r,i) => r.map((x,j) => x * B[i][j])); }
    function scale(A, s){ return A.map(r => r.map(x => x*s)); }

    // Backprop for one step: X -> Z (conv) -> A (activation) -> loss MSE to target
    function forwardBackward(Xin, Kin, Tin, activation='relu') {
      // Dimensions derived from actual inputs (so tests using smaller matrices won't break)
      const h = Xin.length, w = Xin[0].length;
      const kh = Kin.length, kw = Kin[0].length;
      const oh = h - kh + 1, ow = w - kw + 1;
      if (oh <= 0 || ow <= 0) throw new Error('forwardBackward: kernel larger than input');

      // forward
      const Z = conv2d_valid(Xin, Kin);
      const A = activation === 'relu' ? relu(Z) : Z;
      const loss = mse(A, Tin);

      // dLoss/dA = 2*(A - T)/N
      const N = oh * ow;
      const dA = scale(sub(A, Tin), 2 / N);

      // activation grad
      const dZ = activation === 'relu' ? mul(dA, reluGrad(Z)) : dA;

      // dLoss/dK = correlation(X, dZ) with *local* output dims
      const dK = zeros(kh, kw);
      for (let a = 0; a < kh; a++) {
        for (let b = 0; b < kw; b++) {
          let s = 0;
          for (let i = 0; i < oh; i++) {
            for (let j = 0; j < ow; j++) {
              s += Xin[i + a][j + b] * dZ[i][j];
            }
          }
          dK[a][b] = s;
        }
      }

      return { Z, A, loss, dK };
    }

    function updateKernel(dK) {
      if (opt === 'sgd') {
        // vanilla SGD
        for (let a = 0; a < K; a++) for (let b = 0; b < K; b++) KER[a][b] -= lr * dK[a][b];
      } else if (opt === 'momentum') {
        const beta = 0.9;
        for (let a = 0; a < K; a++) {
          for (let b = 0; b < K; b++) {
            v[a][b] = beta * v[a][b] + (1 - beta) * dK[a][b];
            KER[a][b] -= lr * v[a][b];
          }
        }
      } else if (opt === 'adam') {
        const b1 = 0.9, b2 = 0.999, eps = 1e-8; t++;
        for (let a = 0; a < K; a++) {
          for (let b = 0; b < K; b++) {
            m[a][b] = b1 * m[a][b] + (1 - b1) * dK[a][b];
            u[a][b] = b2 * u[a][b] + (1 - b2) * (dK[a][b] ** 2);
            const mhat = m[a][b] / (1 - Math.pow(b1, t));
            const vhat = u[a][b] / (1 - Math.pow(b2, t));
            KER[a][b] -= lr * mhat / (Math.sqrt(vhat) + eps);
          }
        }
      }
    }

    // -----------------------
    // Role mapping for headers
    // -----------------------
    function roleFromTitle(title){
      if (title.startsWith('Pre-')) return 'Z';
      if (title.startsWith('Activation')) return 'A';
      if (title.startsWith('Gradient')) return 'dK';
      if (title.startsWith('Update')) return 'dW';
      if (title.startsWith('Input')) return 'X';
      if (title.startsWith('Kernel')) return 'W';
      if (title.startsWith('Target')) return 'T';
      return 'misc';
    }

    // -----------------------
    // UI: grid rendering & interaction
    // -----------------------
    const gridsEl = document.getElementById('grids');
    let tooltipEl = null;
    let tooltipContent = null;
    let lastCache = null;

    function createGridPanel(title, matrixRef, editable=false, size=30) {
      const panel = document.createElement('div');
      panel.className = 'grid-panel';
      const header = document.createElement('div');
      header.className = 'grid-title';
      const left = document.createElement('div');
      left.innerHTML = `<b>${title}</b>`;
      const right = document.createElement('div');
      right.className = 'actions';
      const dim = document.createElement('span');
      dim.textContent = `${matrixRef.length}×${matrixRef[0].length}`;
      const infoBtn = document.createElement('button');
      infoBtn.className = 'btn icon small'; infoBtn.textContent = '?'; infoBtn.title = 'Explain this block';
      const role = roleFromTitle(title);
      infoBtn.addEventListener('click', (e)=>{
        e.stopPropagation();
        const html = buildTooltipHTML(role);
        showTooltip(html, infoBtn);
      });
      right.appendChild(dim); right.appendChild(infoBtn);
      header.appendChild(left); header.appendChild(right);

      const matrix = document.createElement('div');
      matrix.className = 'matrix';
      matrix.style.gridTemplateColumns = `repeat(${matrixRef[0].length}, ${size}px)`;

      const cells = [];
      const vmax = () => maxAbs(matrixRef);

      for (let i = 0; i < matrixRef.length; i++) {
        for (let j = 0; j < matrixRef[0].length; j++) {
          const cell = document.createElement('div');
          cell.className = 'cell';
          const span = document.createElement('span');
          const txt = document.createElement('small');
          cell.appendChild(span); cell.appendChild(txt);

          function paint() {
            const m = vmax();
            span.style.background = heatColor(matrixRef[i][j], m);
            txt.textContent = formatNum(matrixRef[i][j]);
          }
          paint();
          cells.push({cell, i, j, paint});
          matrix.appendChild(cell);
        }
      }

      // Interaction: paint values by dragging
      if (editable) {
        let down = false; let erase = false;
        const noise = document.getElementById('noise');
        matrix.addEventListener('pointerdown', e => {
          down = true; erase = e.shiftKey;
          const {i, j} = pickCell(e, matrixRef);
          if (i != null) applyPaint(i, j, erase, +noise.value);
        });
        window.addEventListener('pointerup', () => down = false);
        matrix.addEventListener('pointermove', e => {
          if (!down) return;
          const {i, j} = pickCell(e, matrixRef);
          if (i != null) applyPaint(i, j, erase, +noise.value);
        });
      }

      function pickCell(e, M) {
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left; const y = e.clientY - rect.top;
        const cw = rect.width / M[0].length; const ch = rect.height / M.length;
        const j = Math.floor(x / cw); const i = Math.floor(y / ch);
        if (i < 0 || j < 0 || i >= M.length || j >= M[0].length) return {i:null,j:null};
        return {i, j};
      }

      function applyPaint(i, j, erase, noise=0) {
        const delta = erase ? -0.25 : 0.25;
        const n = (randn() * noise);
        matrixRef[i][j] = clamp(matrixRef[i][j] + delta + n, -2, 2);
        render();
      }

      panel.appendChild(header);
      panel.appendChild(matrix);
      panel.paint = () => { for (const c of cells) c.paint(); };
      return panel;
    }

    function formatNum(x) {
      const ax = Math.abs(x);
      if (ax >= 100) return x.toFixed(0);
      if (ax >= 10) return x.toFixed(1);
      if (ax >= 1) return x.toFixed(2);
      return x.toFixed(3);
    }

    function maxAbs(M) {
      let m = 0; for (let i=0;i<M.length;i++) for (let j=0;j<M[0].length;j++) m = Math.max(m, Math.abs(M[i][j]));
      return m || 1e-9;
    }

    // Panels
    let P_in, P_k, P_z, P_a, P_t, P_dk, P_dw;

    // Detail selection
    let selI = Math.floor((OH-1)/2), selJ = Math.floor((OW-1)/2);

    function buildPanels() {
      gridsEl.innerHTML = '';
      P_in = createGridPanel('Input X', X, true, 34);
      P_k  = createGridPanel('Kernel W (3×3)', KER, true, 34);
      const f = forwardBackward(X, KER, target, act);
      P_z  = createGridPanel('Pre-activation Z = X * W', f.Z, false, 30);
      P_a  = createGridPanel('Activation A', f.A, false, 30);
      P_t  = createGridPanel('Target T', target, true, 30);
      P_dk = createGridPanel('Gradient dL/dW', zeros(3,3), false, 34);
      P_dw = createGridPanel('Update ΔW (last step)', zeros(3,3), false, 34);
      gridsEl.append(P_in, P_k, P_z, P_a, P_t, P_dk, P_dw);

      // populate selector for positions
      const sel = document.getElementById('posSelect');
      if (sel) {
        sel.innerHTML = '';
        for (let i=0;i<OH;i++) {
          for (let j=0;j<OW;j++) {
            const opt = document.createElement('option');
            opt.value = `${i},${j}`;
            opt.textContent = `(${i},${j})`;
            if (i===selI && j===selJ) opt.selected = true;
            sel.appendChild(opt);
          }
        }
        sel.addEventListener('change', (e)=>{
          const [i,j] = e.target.value.split(',').map(Number);
          selI=i; selJ=j; render();
        });
      }
    }

    function replacePanel(panel, M, title, size) {
      const parent = panel.parentElement;
      const repl = createGridPanel(title, M, false, size);
      parent.replaceChild(repl, panel);
      if (title.startsWith('Pre-')) P_z = repl;
      else if (title.startsWith('Activation')) P_a = repl;
      else if (title.startsWith('Gradient')) P_dk = repl;
      else if (title.startsWith('Update')) P_dw = repl;
    }

    // -----------------------
    // Loss chart
    // -----------------------
    const lossCanvas = document.getElementById('loss');
    const ctx = lossCanvas.getContext('2d');
    const lossHistory = [];

    function resizeChart() {
      const dpr = window.devicePixelRatio || 1;
      lossCanvas.width = lossCanvas.clientWidth * dpr;
      lossCanvas.height = lossCanvas.clientHeight * dpr;
    }
    window.addEventListener('resize', () => { resizeChart(); drawLoss(); });

    function drawLoss(currLoss) {
      if (typeof currLoss === 'number') lossHistory.push(currLoss);
      const w = lossCanvas.width, h = lossCanvas.height;
      ctx.clearRect(0,0,w,h);
      if (lossHistory.length < 2) return;
      const maxL = Math.max(...lossHistory);
      const minL = Math.min(...lossHistory);
      const pad = 10 * (window.devicePixelRatio || 1);
      const xstep = (w - pad*2) / (lossHistory.length - 1);
      ctx.lineWidth = 2 * (window.devicePixelRatio || 1);
      ctx.beginPath();
      for (let i = 0; i < lossHistory.length; i++) {
        const x = pad + i * xstep;
        const t = (lossHistory[i] - minL) / Math.max(1e-9, (maxL - minL));
        const y = pad + (1 - t) * (h - pad*2);
        if (i === 0) ctx.moveTo(x, y); else ctx.lineTo(x, y);
      }
      const grad = ctx.createLinearGradient(0,0,w,0);
      grad.addColorStop(0, '#0ea5e9'); grad.addColorStop(1, '#22d3ee');
      ctx.strokeStyle = grad; ctx.stroke();
    }

    // -----------------------
    // Tooltip helpers
    // -----------------------
    function ensureTooltip(){
      if (!tooltipEl) {
        tooltipEl = document.getElementById('tooltip');
        if (!tooltipEl) {
          tooltipEl = document.createElement('div');
          tooltipEl.id = 'tooltip';
          tooltipEl.className = 'tooltip';
          document.body.appendChild(tooltipEl);
        }
      }
      if (!tooltipContent) {
        tooltipContent = tooltipEl.querySelector('.content');
        if (!tooltipContent) {
          tooltipContent = document.createElement('div');
          tooltipContent.className = 'content';
          tooltipEl.appendChild(tooltipContent);
        }
      }
    }

    function showTooltip(html, anchorEl){
      ensureTooltip();
      tooltipContent.innerHTML = html;
      tooltipEl.style.display = 'block';
      tooltipEl.style.visibility = 'hidden';
      const rect = anchorEl.getBoundingClientRect();
      const scrollX = window.scrollX || document.documentElement.scrollLeft;
      const scrollY = window.scrollY || document.documentElement.scrollTop;
      const margin = 8;
      const tw = Math.min(520, window.innerWidth - 20);
      tooltipEl.style.maxWidth = tw + 'px';
      // Measure
      tooltipEl.style.left = '0px';
      tooltipEl.style.top = '0px';
      const tr = tooltipEl.getBoundingClientRect();
      const th = tr.height; const twidth = Math.min(tr.width, tw);
      // Prefer above if there's room; else below
      const spaceAbove = rect.top;
      const spaceBelow = window.innerHeight - rect.bottom;
      let y;
      if (spaceAbove > th + margin) {
        y = rect.top + scrollY - th - margin; // above
      } else {
        y = rect.bottom + scrollY + margin;   // below
      }
      // Center horizontally over the anchor, clamp to viewport
      let x = rect.left + scrollX + rect.width/2 - twidth/2;
      const maxX = scrollX + window.innerWidth - twidth - 12;
      const minX = scrollX + 12;
      x = Math.max(minX, Math.min(maxX, x));
      tooltipEl.style.left = x + 'px';
      tooltipEl.style.top = y + 'px';
      tooltipEl.style.visibility = 'visible';
    }
    function hideTooltip(){ ensureTooltip(); tooltipEl.style.display = 'none'; }
    window.addEventListener('scroll', hideTooltip);
    window.addEventListener('resize', hideTooltip);
    document.addEventListener('click', (e)=>{ ensureTooltip(); if (!tooltipEl.contains(e.target)) hideTooltip(); });

    function getPatch(X, i, j) {
      const P = zeros(K, K);
      for (let a=0;a<K;a++) for (let b=0;b<K;b++) P[a][b] = X[i+a][j+b];
      return P;
    }

    function matToHTML(M) {
      const rows = M.map(r => `<tr>${r.map(x=>`<td style='padding:2px 6px;border:1px solid #1f2937;'>${formatNum(x)}</td>`).join('')}</tr>`).join('');
      return `<table style='border-collapse:collapse;font-size:12px;margin:6px 0;'>${rows}</table>`;
    }

    function inlineConvSumHTML(P, W){
      // P and W are 3x3
      let parts = [];
      for (let a=0;a<3;a++){
        for (let b=0;b<3;b++){
          parts.push(`${formatNum(P[a][b])}×${formatNum(W[a][b])}`);
        }
      }
      return parts.join(' + ');
    }

    function dZFromCache(cache){
      const {Z,A} = cache;
      const N = A.length * A[0].length;
      const dA = A.map((row,i)=> row.map((av,j)=> 2*(av - target[i][j])/N));
      const dZ = (act==='relu' ? Z.map((row,i)=> row.map((zv,j)=> (zv>=0?1:0)*dA[i][j])) : dA);
      return dZ;
    }

    function contribMapForWeight(a,b, cache){
      const dZ = dZFromCache(cache);
      const C = zeros(OH, OW);
      for (let i=0;i<OH;i++) for (let j=0;j<OW;j++) C[i][j] = X[i+a][j+b] * dZ[i][j];
      const sum = C.reduce((s,row)=> s+row.reduce((t,x)=>t+x,0), 0);
      return {C, sum};
    }

    function buildTooltipHTML(role){
      const cache = lastCache || forwardBackward(X, KER, target, act);
      const {Z,A,loss,dK} = cache;
      const P = getPatch(X, selI, selJ);
      const z = Z[selI][selJ];
      const a = (act==='relu'? Math.max(0,z): z);
      const tval = target[selI][selJ];
      const nOut = OH*OW;
      const dAij = 2*(a - tval)/nOut;
      const dZij = (act==='relu'? (z>=0?1:0):1) * dAij;
      const pt = matToHTML(P);

      if (role==='Z'){
        const sumStr = inlineConvSumHTML(P, KER);
        return `<h4>Pre-activation Z at (${selI},${selJ})</h4>
                <div class='content'>
                  <div><b>Patch X</b> (from Input): ${pt}</div>
                  <div><b>Kernel W</b> (3×3): ${matToHTML(KER)}</div>
                  <div><b>Formula:</b> Z = Σ X·W</div>
                  <div><b>Sample calc:</b> ${sumStr} = <b>${formatNum(z)}</b></div>
                </div>`;
      } else if (role==='A'){
        return `<h4>Activation A at (${selI},${selJ})</h4>
                <div class='content'>
                  <div>σ = ${act==='relu'?'ReLU':'Linear'}</div>
                  <div><b>A</b> = σ(Z) = <b>${formatNum(a)}</b></div>
                  <div><small>Z = ${formatNum(z)}, σ'(Z) = ${act==='relu' ? (z>=0?'1':'0') : '1'}</small></div>
                </div>`;
      } else if (role==='dK'){
        const {C, sum} = contribMapForWeight(1,1, cache); // sample center weight
        return `<h4>Gradient ∂L/∂W</h4>
                <div class='content'>
                  <div>For weight <b>W[1,1]</b> (center): ∂L/∂W[1,1] = Σ_{i,j} X[i+1,j+1]·∂L/∂Z[i,j]</div>
                  <div><b>Contribution map</b> (X shifted × ∂L/∂Z): ${matToHTML(C)}</div>
                  <div><b>Sum</b> = ${formatNum(sum)} (should match dK[1][1] = ${formatNum(dK[1][1])})</div>
                </div>`;
      } else if (role==='dW'){
        return `<h4>Update ΔW (last step)</h4>
                <div class='content'>
                  Computed by optimizer <b>${opt.toUpperCase()}</b> with η=${formatNum(lr)}.<br/>
                  ΔW = ${opt==='sgd' ? '−η·∂L/∂W' : opt==='momentum' ? '−η·v,  v:=β·v+(1−β)·g' : '−η·m̂/(√v̂+ε)'}
                </div>`;
      } else if (role==='W'){
        return `<h4>Kernel W</h4>
                <div class='content'>
                  Weights updated by ${opt.toUpperCase()} (η = ${formatNum(lr)}). ${matToHTML(KER)}
                </div>`;
      } else if (role==='X'){
        return `<h4>Input X</h4>
                <div class='content'>
                  Paint values; they form the 3×3 patch used for (${selI},${selJ}). ${matToHTML(X)}
                </div>`;
      } else if (role==='T'){
        return `<h4>Target T</h4>
                <div class='content'>
                  Loss L = (1/N)∑(A−T)^2, N=${nOut}. ${matToHTML(target)}
                </div>`;
      }
      return `<h4>Info</h4><div class='content'>No extra details.</div>`;
    }

    function updateDetails(cache){
      const det = document.getElementById('details'); if (!det) return;
      const {Z,A,loss,dK} = cache;
      const P = getPatch(X, selI, selJ);
      const z = Z[selI][selJ];
      const a = (act==='relu'? Math.max(0,z): z);
      const tval = target[selI][selJ];
      const nOut = OH*OW;
      const dAij = 2*(a - tval)/nOut;
      const dZij = (act==='relu'? (z>=0?1:0):1) * dAij;
      const contrib = zeros(K,K);
      for (let a1=0;a1<K;a1++) for (let b1=0;b1<K;b1++) contrib[a1][b1] = P[a1][b1]*dZij;
      const lrStr = formatNum(lr);
      const eqHTML = `
        <div style="line-height:1.6">
        <div><b>Selected output:</b> (i,j)=(${selI},${selJ})</div>
        <div><b>Convolution:</b> Z[${selI},${selJ}] = ∑_{a,b} X[${selI}+a,${selJ}+b]·W[a,b] = <b>${formatNum(z)}</b></div>
        ${matToHTML(P)}
        <div><b>Activation (${act.toUpperCase()}):</b> A[${selI},${selJ}] = ${act==='relu'?`max(0, Z)`: `Z`} = <b>${formatNum(a)}</b></div>
        <div><b>Loss (MSE):</b> L = (1/N) ∑ (A - T)^2, N=${nOut}, T[${selI},${selJ}]=${formatNum(tval)}, so <b>L≈${formatNum(loss)}</b></div>
        <hr style="border-color:#1f2937"/>
        <div><b>Gradient at (i,j):</b> ∂L/∂A = 2(A−T)/N = <b>${formatNum(dAij)}</b></div>
        <div>∂L/∂Z = ∂L/∂A · σ'(Z) = <b>${formatNum(dZij)}</b> (σ'=${act==='relu'?'1[z≥0]':'1'})</div>
        <div style="margin-top:6px"><b>Contribution to ∂L/∂W from (i,j):</b> (X patch × ∂L/∂Z[${selI},${selJ}])</div>
        ${matToHTML(contrib)}
        <div><b>Total ∂L/∂W:</b> correlation over all (i,j) shown in panel "Gradient dL/dW".</div>
        <hr style="border-color:#1f2937"/>
        <div><b>Update rule (${opt.toUpperCase()}):</b> ${opt==='sgd'?`W := W − η · ∂L/∂W` : opt==='momentum'?`v := βv + (1−β)·∂L/∂W; W := W − ηv` : `m := β₁m+(1−β₁)g; v := β₂v+(1−β₂)g²; m̂:=m/(1−β₁^t); v̂:=v/(√v̂+ε)`} with η=${lrStr}</div>
        <div>Last step ΔW (shown in panel) is the applied change.</div>
        </div>`;

      det.innerHTML = eqHTML;
    }

    // -----------------------
    // Render
    // -----------------------
    function render() {
      const {Z, A, loss, dK} = forwardBackward(X, KER, target, act);
      lastCache = {Z,A,loss,dK};
      // Update visual matrices
      P_in.paint(); P_k.paint();
      replacePanel(P_z, Z, 'Pre-activation Z = X * W', 30);
      replacePanel(P_a, A, 'Activation A', 30);
      replacePanel(P_dk, dK, 'Gradient dL/dW', 34);
      replacePanel(P_dw, lastDeltaW, 'Update ΔW (last step)', 34);
      drawLoss(loss);
      const stats = document.getElementById('stats');
      if (stats) stats.textContent = `Steps: ${stepCount} | Loss: ${loss.toFixed(4)}`;
      updateDetails({Z,A,loss,dK});
    }

    // -----------------------
    // Trainer loop
    // -----------------------
    let running = false; let rafId = null;

    function stepTrain() {
      const before = clone(KER);
      const { dK } = forwardBackward(X, KER, target, act);
      updateKernel(dK);
      // compute actual parameter change for visibility
      for (let a = 0; a < K; a++) {
        for (let b = 0; b < K; b++) {
          lastDeltaW[a][b] = KER[a][b] - before[a][b];
        }
      }
      stepCount++;
      render();
    }

    function loop() {
      if (!running) return;
      for (let i = 0; i < 2; i++) stepTrain(); // 2 steps per frame for smoother decrease
      rafId = requestAnimationFrame(loop);
    }

    function runToggle() {
      running = !running;
      btnRun.textContent = running ? '⏸ Pause' : '▶ Run';
      if (running) loop(); else cancelAnimationFrame(rafId);
    }

    // -----------------------
    // Controls wiring
    // -----------------------
    const btnRun = document.getElementById('btnRun');
    const btnStep = document.getElementById('btnStep');
    const btnReset = document.getElementById('btnReset');
    const btnRandTarget = document.getElementById('btnRandTarget');

    const lrEl = document.getElementById('lr');
    const lrVal = document.getElementById('lrVal');
    const noiseEl = document.getElementById('noise');
    const noiseVal = document.getElementById('noiseVal');

    const optSGD = document.getElementById('optSGD');
    const optMomentum = document.getElementById('optMomentum');
    const optAdam = document.getElementById('optAdam');
    const actReLU = document.getElementById('actReLU');
    const actLinear = document.getElementById('actLinear');
    const btnExplain = document.getElementById('btnExplain');
    let showDetails = false;

    function syncLabels(){ lrVal.textContent = (+lrEl.value).toFixed(3); noiseVal.textContent = (+noiseEl.value).toFixed(2); }

    btnRun.addEventListener('click', runToggle);
    btnStep.addEventListener('click', stepTrain);

    // Toggle details panel with '?' button
    btnExplain.addEventListener('click', () => {
      showDetails = !showDetails;
      const sd = document.getElementById('stepDetails');
      if (sd) sd.style.display = showDetails ? 'block' : 'none';
      btnExplain.classList.toggle('primary', showDetails);
    });

    btnReset.addEventListener('click', () => {
      running = false; btnRun.textContent = '▶ Run';
      X = randnMatrix(H, W, 0.1);
      KER = randnMatrix(K, K, 0.1);
      target = randnMatrix(OH, OW, 0.2);
      v = zeros(K, K); m = zeros(K, K); u = zeros(K, K); t = 0;
      lastDeltaW = zeros(K, K); stepCount = 0;
      lossHistory.length = 0; render();
    });

    btnRandTarget.addEventListener('click', () => {
      target = randnMatrix(OH, OW, 0.3);
      render();
    });

    lrEl.addEventListener('input', () => { lr = +lrEl.value; syncLabels(); });
    noiseEl.addEventListener('input', syncLabels);

    function setOpt(name){ opt = name; for (const b of [optSGD,optMomentum,optAdam]) b.classList.remove('primary'); ({sgd:optSGD, momentum:optMomentum, adam:optAdam})[name].classList.add('primary'); }
    function setAct(name){ act = name; for (const b of [actReLU, actLinear]) b.classList.remove('primary'); ({relu:actReLU, linear:actLinear})[name].classList.add('primary'); render(); }

    optSGD.addEventListener('click', () => setOpt('sgd'));
    optMomentum.addEventListener('click', () => setOpt('momentum'));
    optAdam.addEventListener('click', () => setOpt('adam'));
    actReLU.addEventListener('click', () => setAct('relu'));
    actLinear.addEventListener('click', () => setAct('linear'));

    // -----------------------
    // Self-tests (basic diagnostics)
    // -----------------------
    function runSelfTests(){
      const diag = document.getElementById('diag');
      const tests = [];
      function assert(cond, msg){ tests.push({pass: !!cond, msg}); }
      function approx(a,b,tol=1e-6){ return Math.abs(a-b) <= tol*(1+Math.abs(a)+Math.abs(b)); }

      // Test 1: forwardBackward returns object with keys (global sizes)
      const fb = forwardBackward(X, KER, target, act);
      assert(fb && typeof fb === 'object', 'forwardBackward returns an object');
      assert('Z' in fb && 'A' in fb && 'loss' in fb && 'dK' in fb, 'fb has keys Z, A, loss, dK');
      assert(Array.isArray(fb.Z) && fb.Z.length === OH && fb.Z[0].length === OW, 'Z shape is OH×OW');
      assert(Array.isArray(fb.dK) && fb.dK.length === K && fb.dK[0].length === K, 'dK shape is K×K');

      // Test 2: numerical gradient check for multiple weights (global sizes)
      const eps = 1e-4; const picks = [[1,1],[0,0],[2,2]];
      let gradOK = true;
      for (const [aa,bb] of picks){
        const save = KER[aa][bb];
        KER[aa][bb] = save + eps; const lp = forwardBackward(X, KER, target, act).loss;
        KER[aa][bb] = save - eps; const lm = forwardBackward(X, KER, target, act).loss;
        KER[aa][bb] = save;
        const numGrad = (lp - lm) / (2*eps);
        const anaGrad = fb.dK[aa][bb];
        const relErr = Math.abs(numGrad - anaGrad) / (Math.abs(numGrad) + Math.abs(anaGrad) + 1e-9);
        if (!(relErr < 1e-2)) gradOK = false;
      }
      assert(gradOK, 'gradient checks [1,1],[0,0],[2,2] < 1e-2');

      // Test 3: deterministic forward sanity (linear act, ones kernel) on 4×4 → 2×2
      const Xs = [
        [1,2,3,4],
        [5,6,7,8],
        [9,10,11,12],
        [13,14,15,16]
      ];
      const Wones = [ [1,1,1],[1,1,1],[1,1,1] ];
      const Ts = [[0,0],[0,0]]; // dummy target
      const fb2 = forwardBackward(Xs, Wones, Ts, 'linear');
      let sum00 = 0; for (let a=0;a<3;a++) for (let b=0;b<3;b++) sum00 += Xs[a][b];
      assert(approx(fb2.Z[0][0], sum00), 'Z[0,0] equals sum of top-left 3×3 patch for ones kernel');

      // Test 4: forwardBackward uses *local* shapes (no reliance on global OH/OW)
      const X3 = randnMatrix(6, 5, 0.1);
      const W3 = randnMatrix(3, 3, 0.1);
      const oh3 = 6 - 3 + 1, ow3 = 5 - 3 + 1;
      const T3 = randnMatrix(oh3, ow3, 0.1);
      const fb3 = forwardBackward(X3, W3, T3, 'relu');
      assert(Array.isArray(fb3.Z) && fb3.Z.length === oh3 && fb3.Z[0].length === ow3, 'local Z shape matches input/kernel');
      assert(Array.isArray(fb3.dK) && fb3.dK.length === 3 && fb3.dK[0].length === 3, 'local dK is 3×3');
      assert(!Number.isNaN(fb3.loss), 'local loss is a number');

      const pass = tests.every(t=>t.pass);
      if (diag) {
        diag.style.color = pass ? '#34d399' : '#f87171';
        diag.textContent = (pass? 'Diagnostics: All self-tests passed.' : 'Diagnostics: Some tests failed. Open console for details.');
      }
      // console detail
      console.group('Self-tests');
      tests.forEach((t,i)=> console[t.pass?'log':'error'](`#${i+1}`, t.msg));
      console.groupEnd();
    }

    // -----------------------
    // Init
    // -----------------------
    function init(){
      buildPanels();
      resizeChart();
      syncLabels();
      setOpt('sgd');
      setAct('linear');
      runSelfTests();
      render();
    }

    init();
  </script>
  <div id="tooltip" class="tooltip"><div class="content"></div></div>
</body>
</html>
