<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Decision Tree — PCA & Multiclass Visualizer</title>
  <style>
    :root { --bg:#0f172a; --panel:#0b1220; --text:#e5e7eb; --muted:#9ca3af; --accent:#22c55e; --accent2:#60a5fa; --mag:#a78bfa; --danger:#f87171; --warning:#fbbf24; }
    *{box-sizing:border-box}
    body{margin:0;font-family:ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto,Ubuntu;color:var(--text);background:radial-gradient(1000px 600px at 10% -10%,#1f2937,var(--bg))}
    .wrap{max-width:1280px;margin:24px auto;padding:16px}
    .grid{display:grid;grid-template-columns:1.2fr .8fr;gap:16px}
    .card{background:linear-gradient(180deg,rgba(255,255,255,.04),rgba(255,255,255,.02));border:1px solid rgba(255,255,255,.07);border-radius:16px;box-shadow:0 10px 30px rgba(0,0,0,.25)}
    .card h2{margin:0;padding:14px 16px;border-bottom:1px solid rgba(255,255,255,.06);font-size:18px;font-weight:600;color:#f8fafc}
    .card .body{padding:16px}

    .canvases{display:grid;gap:12px}
    canvas{width:100%;height:420px;background:#0a1120;border:1px solid rgba(255,255,255,.06);border-radius:16px}
    #metricCanvas{height:180px}
    #treeCanvas{height:300px}

    .legend{display:flex;gap:12px;flex-wrap:wrap;font-size:13px;color:var(--muted);margin-top:8px}
    .dot{display:inline-block;width:10px;height:10px;border-radius:50%;margin-right:6px}

    .equation{margin-top:10px;font-family:ui-monospace,Consolas,Menlo,monospace;color:#c7d2fe;background:var(--panel);border:1px solid rgba(255,255,255,.08);padding:10px 12px;border-radius:10px}

    .controls{display:grid;grid-template-columns:1fr 1fr;gap:12px}
    .row{display:grid;grid-template-columns:180px 1fr 120px;gap:10px;align-items:center}
    label{color:var(--muted);font-size:12px}
    select,input[type=number],input[type=range]{width:100%;background:#0b1220;color:#e5e7eb;border:1px solid rgba(255,255,255,.08);border-radius:10px;padding:8px 10px}
    input[type=range]{appearance:none;height:6px;border-radius:999px;background:#222a3b}
    input[type=range]::-webkit-slider-thumb{appearance:none;width:16px;height:16px;border-radius:999px;background:var(--accent2);border:2px solid white;box-shadow:0 1px 4px rgba(0,0,0,.4)}
    .btns{display:flex;gap:10px;flex-wrap:wrap;margin-top:8px}
    button{border:1px solid rgba(255,255,255,.12);color:#e5e7eb;background:#101626;padding:10px 14px;border-radius:12px;cursor:pointer;font-weight:600;letter-spacing:.2px;transition:transform .06s ease,background .2s ease,border-color .2s ease}
    button:hover{transform:translateY(-1px);border-color:rgba(255,255,255,.22)}
    .primary{background:linear-gradient(135deg,#1d4ed8,#22c55e);border:none}
    .danger{background:linear-gradient(135deg,#991b1b,#ef4444);border:none}
    .warn{background:linear-gradient(135deg,#92400e,#f59e0b);border:none}
    .muted{background:#0b1220}

    .status{display:grid;grid-template-columns:repeat(3,1fr);gap:10px;margin-top:12px}
    .stat{background:#0b1220;border:1px solid rgba(255,255,255,.08);border-radius:12px;padding:12px}
    .stat .k{color:var(--muted);font-size:12px}
    .stat .v{font-size:18px;font-weight:700}

    .mode{display:flex;gap:8px;align-items:center;margin:8px 0 10px}
    .badge{display:inline-block;padding:4px 8px;border-radius:999px;font-size:12px;border:1px solid rgba(255,255,255,.15)}
    .phase-0{background:#0b3b1d;border-color:#1f7a3e}
    .phase-1{background:#38210b;border-color:#8a5a10}
    .phase-2{background:#2a223f;border-color:#6b5ca3}
    .phase-3{background:#401d2b;border-color:#b0476c}

    .calc-grid{display:grid;grid-template-columns:1fr;gap:10px}
    .calc-box{background:#0b1220;border:1px solid rgba(255,255,255,.1);border-radius:12px;padding:10px}
    .calc-box h3{margin:0 0 6px 0;font-size:14px;color:#dbeafe}
    .mono{font-family:ui-monospace,Consolas,Menlo,monospace;white-space:pre-wrap}
    .scroll{max-height:260px;overflow:auto}

    @media (max-width:980px){.grid{grid-template-columns:1fr}}
  </style>
</head>
<body>
  <div class="wrap">
    <div class="grid">
      <!-- Viz -->
      <div class="card">
        <h2>Decision Tree — PCA & Multiclass Playground <span id="phaseBadge" class="badge phase-0" style="margin-left:8px">Classic mode</span></h2>
        <div class="body">
          <div class="canvases">
            <canvas id="plotCanvas"></canvas>
            <canvas id="metricCanvas"></canvas>
            <canvas id="treeCanvas"></canvas>
          </div>
          <div class="legend" id="legend"></div>
          <div class="equation" id="equation">criterion = Gini/Entropy (multiclass), Gain = Imp(P) − (nL/n)·Imp(L) − (nR/n)·Imp(R); plot shows PCA projection (select PCs)</div>
        </div>
      </div>

      <!-- Controls -->
      <div class="card">
        <h2>Controls</h2>
        <div class="body">
          <div class="mode">
            <label style="color:var(--muted);font-size:12px">Mode:</label>
            <button id="modeClassic" class="muted">Classic</button>
            <button id="modePhased" class="muted">Multi‑phase</button>
          </div>
          <div class="controls">
            <div class="row"><label># Points</label><input id="points" type="range" min="100" max="10000" value="1200" step="50"/><input id="pointsNum" type="number" min="100" max="10000" value="1200"/></div>
            <div class="row"><label>Noise σ</label><input id="noise" type="range" min="0" max="3" value="0.6" step="0.01"/><input id="noiseNum" type="number" min="0" max="3" value="0.6" step="0.01"/></div>
            <div class="row"><label>Separation Δ</label><input id="sep" type="range" min="0" max="8" value="3.5" step="0.1"/><input id="sepNum" type="number" min="0" max="8" value="3.5" step="0.1"/></div>
            <div class="row"><label>Features (D)</label><input id="dims" type="range" min="2" max="30" value="8" step="1"/><input id="dimsNum" type="number" min="2" max="30" value="8" step="1"/></div>
            <div class="row"><label>Classes (K)</label><input id="classes" type="range" min="2" max="8" value="3" step="1"/><input id="classesNum" type="number" min="2" max="8" value="3" step="1"/></div>
            <div class="row"><label>Plot PCs</label>
              <div style="display:grid;grid-template-columns:1fr 1fr;gap:8px">
                <select id="pcX"></select>
                <select id="pcY"></select>
              </div>
              <button id="btnReproject" class="muted">Recompute PCA</button>
            </div>
            <div class="row"><label>Max depth</label><input id="maxDepth" type="range" min="1" max="14" value="6" step="1"/><input id="maxDepthNum" type="number" min="1" max="14" value="6" step="1"/></div>
            <div class="row"><label>Min samples/leaf</label><input id="minLeaf" type="range" min="1" max="500" value="30" step="1"/><input id="minLeafNum" type="number" min="1" max="500" value="30" step="1"/></div>
            <div class="row"><label>Criterion</label>
              <select id="criterion">
                <option value="gini" selected>Gini</option>
                <option value="entropy">Entropy</option>
              </select>
            </div>
            <div class="row"><label>Max thresholds/feature</label><input id="kThresh" type="range" min="5" max="160" value="60" step="1"/><input id="kThreshNum" type="number" min="5" max="160" value="60" step="1"/></div>
          </div>
          <div class="btns">
            <button class="primary" id="btnStart">Start</button>
            <button class="muted" id="btnStep">Step</button>
            <button class="muted" id="btnNextPhase" style="display:none">Next Phase</button>
            <button class="warn" id="btnShuffle">Shuffle Data</button>
            <button class="danger" id="btnReset">Reset</button>
            <button class="muted" id="btnRegenerate">Regenerate Dataset</button>
          </div>
          <div class="status">
            <div class="stat"><div class="k">Nodes</div><div class="v" id="statNodes">0</div></div>
            <div class="stat"><div class="k">Depth</div><div class="v" id="statDepth">0</div></div>
            <div class="stat"><div class="k">Accuracy</div><div class="v" id="statAcc">—</div></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Calculations -->
    <div id="calcCard" class="card" style="margin-top:16px; display:none">
      <h2>Live Calculations</h2>
      <div class="body calc-grid">
        <div class="calc-box"><h3 id="phaseTitle">Phase 1 — Evaluate candidate splits</h3><div class="mono" id="calcMain">—</div></div>
        <div class="calc-box"><h3>Candidate table (top 80 by gain)</h3><div class="mono scroll" id="tableBox">—</div></div>
      </div>
    </div>
  </div>

  <script>
    // ===== Helpers =====
    const el=(id)=>document.getElementById(id);
    const RNG=(seed=1234567)=>{let s=seed>>>0;return()=>{s=(s*1664525+1013904223)>>>0;return s/0xffffffff;}};
    const bindPair=(a,b,fn)=>{const sync=(src,dst)=>{dst.value=src.value;fn(parseFloat(src.value));};a.addEventListener('input',()=>sync(a,b));b.addEventListener('input',()=>sync(b,a));fn(parseFloat(a.value));};

    // ===== Palette for up to 8 classes =====
    const COLORS=["#60a5fa","#f472b6","#34d399","#fbbf24","#a78bfa","#f87171","#22d3ee","#d946ef"];

    // ===== State =====
    const state={
      seed:42,
      D:8,
      K:3,
      X:[], // feature-major D x N
      y:[], // 0..K-1
      gen:{sigma:0.6,sep:3.5},
      tree:{maxDepth:6,minLeaf:30,criterion:'gini',kThresh:60},
      model:{nodes:[],root:null},
      queue:[],
      training:{running:false,phase:0},
      metrics:{history:[],acc:NaN},
      pca:{U:[], means:[], pcs:[0,1]},
      scale:{xmin:-5,xmax:5,ymin:-5,ymax:5},
      current:null,
      batchCache:null,
      nodeIdSeq:1
    };

    // ===== Data generation: K blobs in D dims on a circle/mesh =====
    function gaussian(rand){let u=0,v=0;while(u===0)u=rand();while(v===0)v=rand();return Math.sqrt(-2*Math.log(u))*Math.cos(2*Math.PI*v)}
    function classCenter(c,D,sep){ // spread around a circle in first 2 dims; small jitter on higher dims
      const ang=2*Math.PI*c/state.K; const base=[Math.cos(ang)*sep/2, Math.sin(ang)*sep/2]; const v=Array(D).fill(0); v[0]=base[0]; v[1]=base[1]; for(let d=2; d<D; d++){ v[d]=((c%2===0)?1:-1)*sep/6 * Math.sin((c+1)*(d+1)); } return v;
    }
    function generateData(N=1200){ const rand=RNG(state.seed); const X=Array.from({length:state.D},()=>new Array(N)); const y=new Array(N); for(let i=0;i<N;i++){ const cls=Math.floor(rand()*state.K); y[i]=cls; const center=classCenter(cls,state.D,state.gen.sep); for(let d=0; d<state.D; d++){ X[d][i]=center[d] + gaussian(rand)*state.gen.sigma + (rand()-0.5)*0.25; } } state.X=X; state.y=y; state.nodeIdSeq=1; computePCA(); fitScale(); resetTree(); drawAll(); buildLegend(); buildPCSelectors(); }

    // ===== PCA (power iteration for top m components) =====
    function computePCA(m=Math.min(6,state.D)){ const N=state.y.length, D=state.D; if(!N) return; // center
      const means=Array(D).fill(0); for(let d=0; d<D; d++){ let s=0; for(let i=0;i<N;i++) s+=state.X[d][i]; means[d]=s/N; }
      // cov = (1/N) Xc Xc^T (D x D). We'll do power iter to get top m eigenvectors
      function covVecMul(v){ const tmp=new Array(N).fill(0); // tmp = Xc^T v  (N)
        for(let i=0;i<N;i++){ let s=0; for(let d=0; d<D; d++){ s += (state.X[d][i]-means[d])*v[d]; } tmp[i]=s/N; }
        const out=new Array(D).fill(0); // out = Xc tmp  (D)
        for(let d=0; d<D; d++){ let s=0; for(let i=0;i<N;i++){ s += (state.X[d][i]-means[d]) * tmp[i]; } out[d]=s; }
        return out; }
      function norm(v){ return Math.hypot(...v); }
      function dot(a,b){ let s=0; for(let i=0;i<a.length;i++) s+=a[i]*b[i]; return s; }
      function sub(a,b){ const r=new Array(a.length); for(let i=0;i<a.length;i++) r[i]=a[i]-b[i]; return r; }
      const U=[]; const L=[]; for(let k=0;k<m;k++){ // init random vector
        let v=Array(D).fill(0).map(()=>Math.random()-0.5); // orthogonalize vs previous U
        for(let t=0;t<4;t++){ for(let j=0;j<U.length;j++){ const proj=dot(v,U[j]); for(let i=0;i<D;i++) v[i]-=proj*U[j][i]; } const nv=norm(v)||1; for(let i=0;i<D;i++) v[i]/=nv; v=covVecMul(v); const nv2=norm(v)||1; for(let i=0;i<D;i++) v[i]/=nv2; }
        // Rayleigh quotient
        const Av=covVecMul(v); const lam=dot(v,Av);
        U.push(v); L.push(lam);
      }
      state.pca={U,means,pcs:[0,1]};
    }
    function projectToPCs(i,j){ // returns [x,y] arrays of length N for plot
      const N=state.y.length, D=state.D; const U=state.pca.U, means=state.pca.means; const xi=new Array(N), xj=new Array(N);
      for(let n=0;n<N;n++){
        let si=0,sj=0; for(let d=0; d<D; d++){ const xc=state.X[d][n]-means[d]; si += xc*U[i][d]; sj += xc*U[j][d]; }
        xi[n]=si; xj[n]=sj;
      }
      return [xi,xj];
    }

    // ===== Impurities (multiclass) =====
    function giniCounts(counts){ const n=counts.reduce((a,b)=>a+b,0); if(n===0) return 0; let s=0; for(const c of counts){ const p=c/n; s += p*p; } return 1-s; }
    function entropyCounts(counts){ const n=counts.reduce((a,b)=>a+b,0); if(n===0) return 0; let h=0; for(const c of counts){ if(c>0){ const p=c/n; h += -p*Math.log2(p); } } return h; }
    function impurityCounts(counts){ return state.tree.criterion==='gini'? giniCounts(counts):entropyCounts(counts); }

    // ===== Tree structures =====
    function makeLeaf(idx){ const counts=Array(state.K).fill(0); for(const i of idx){ counts[state.y[i]]++; } const pred=counts.indexOf(Math.max(...counts)); return { id:state.nodeIdSeq++, leaf:true, idx, counts, pred, depth:0, parent:null, feat:null, thr:null, gain:0, rect:fullRect() } }
    function resetTree(){ const idx=[...Array(state.y.length).keys()]; const root=makeLeaf(idx); state.model={nodes:[root],root}; state.queue=[root]; state.current=root; state.training.phase=0; state.metrics.history=[]; updateStats(); }

    function fullRect(){ const {xmin,xmax,ymin,ymax}=state.scale; return {xmin,xmax,ymin,ymax}; }

    // ===== Split search across all D features =====
    function uniqueSorted(values){ return Array.from(new Set(values)).sort((a,b)=>a-b); }
    function candidateThresholds(vals,k){ const uniq=uniqueSorted(vals); if(uniq.length<=1) return []; const mids=[]; for(let i=0;i<uniq.length-1;i++){ mids.push((uniq[i]+uniq[i+1])/2); } if(mids.length<=k) return mids; const step=(mids.length-1)/(k-1); return Array.from({length:k},(_,i)=>mids[Math.round(i*step)]); }
    function evaluateBestSplit(node){ const idx=node.idx; const n=idx.length; if(n<2) return null; const parentCounts=Array(state.K).fill(0); for(const i of idx){ parentCounts[state.y[i]]++; } const impP=impurityCounts(parentCounts); let best=null; const rows=[]; for(let f=0; f<state.D; f++){ const arr=state.X[f]; const values=idx.map(i=>arr[i]); const ths=candidateThresholds(values,state.tree.kThresh); for(const thr of ths){ const L=Array(state.K).fill(0), R=Array(state.K).fill(0); for(const i of idx){ const c=state.y[i]; if(arr[i]<=thr) L[c]++; else R[c]++; } const nL=L.reduce((a,b)=>a+b,0), nR=R.reduce((a,b)=>a+b,0); if(nL<state.tree.minLeaf || nR<state.tree.minLeaf) continue; const impL=impurityCounts(L), impR=impurityCounts(R); const gain = impP - (nL/n)*impL - (nR/n)*impR; rows.push({feat:`x${f}`,thr,nL,nR,L,R,impP,impL,impR,gain}); if(!best || gain>best.gain){ best={feat:f,thr,gain,nL,nR,L,R,impP,impL,impR}; } } }
      if(!best) return null; const arr=state.X[best.feat]; const leftIdx=[], rightIdx=[]; for(const i of idx){ (arr[i]<=best.thr? leftIdx:rightIdx).push(i); } return { ...best, leftIdx, rightIdx, rows: rows.sort((a,b)=>b.gain-a.gain).slice(0,300) } }

    // ===== Training =====
    function canSplit(node){ if(node.leaf===false) return false; if(node.idx.length < 2*state.tree.minLeaf) return false; if(node.depth >= state.tree.maxDepth) return false; // stop if pure
      return node.counts.filter(c=>c>0).length>1; }
    function nextSplittableNode(){ while(state.queue.length){ const n=state.queue.shift(); if(canSplit(n)) return n; } return null; }
    function classicStep(){ if(state.queue.length===0) return finishEpoch(); const node=nextSplittableNode(); if(!node){ return finishEpoch(); } const best=evaluateBestSplit(node); if(!best){ node.leaf=true; return; } applySplit(node,best); updateStats(); drawAll(); }
    function finishEpoch(){ const acc=accuracy(); state.metrics.history.push(acc); updateStats(); }
    function applySplit(node,best){ node.leaf=false; node.feat=best.feat; node.thr=best.thr; node.gain=best.gain; const left=makeLeaf(best.leftIdx), right=makeLeaf(best.rightIdx); left.depth=node.depth+1; right.depth=node.depth+1; left.parent=node; right.parent=node; // update 2D rects using current PCA axes
      const r=node.rect; const i=state.pca.pcs[0], j=state.pca.pcs[1]; // Only draw precise split line when feature aligns with plotted PC is not well-defined; so highlight region without line
      left.rect={...r}; right.rect={...r}; node.left=left; node.right=right; state.model.nodes.push(left,right); state.queue.push(left,right); state.current=left; }

    // ===== Phased engine =====
    function nextPhase(){ switch(state.training.phase){ case 0: const node = state.current && canSplit(state.current)? state.current : nextSplittableNode(); if(!node){ finishEpoch(); break; } state.current=node; const best=evaluateBestSplit(node); state.batchCache={node,best}; renderPhase(0,{node,best}); state.training.phase=1; break; case 1: if(!state.batchCache || !state.batchCache.best){ state.training.phase=0; break; } renderPhase(1,{best:state.batchCache.best}); state.training.phase=2; break; case 2: if(!state.batchCache || !state.batchCache.best){ state.training.phase=0; break; } applySplit(state.batchCache.node,state.batchCache.best); renderPhase(2,{applied:true}); state.training.phase=3; break; case 3: const acc=accuracy(); state.metrics.history.push(acc); updateStats(); drawAll(); renderPhase(3,{acc}); state.batchCache=null; state.training.phase=0; break; } updatePhaseUI(); }

    // ===== Prediction & Metrics =====
    function predictOne(idx){ let n=state.model.root; while(n && n.leaf===false){ const f=n.feat; n = (state.X[f][idx] <= n.thr)? n.left : n.right; } return n?n.pred:0; }
    function accuracy(){ const N=state.y.length; let ok=0; for(let i=0;i<N;i++){ if(predictOne(i)===state.y[i]) ok++; } return ok/N; }

    // ===== Legend =====
    function buildLegend(){ const L=el('legend'); L.innerHTML=''; for(let k=0;k<state.K;k++){ const span=document.createElement('span'); const dot=document.createElement('span'); dot.className='dot'; dot.style.background=COLORS[k%COLORS.length]; span.appendChild(dot); span.appendChild(document.createTextNode('Class '+k)); L.appendChild(span); } }

    // ===== PC selectors =====
    function buildPCSelectors(){ const px=el('pcX'), py=el('pcY'); px.innerHTML=''; py.innerHTML=''; const m=state.pca.U.length; for(let i=0;i<m;i++){ const o1=document.createElement('option'); o1.value=String(i); o1.textContent=`PC${i+1}`; const o2=o1.cloneNode(true); px.appendChild(o1); py.appendChild(o2); }
      const [i,j]=state.pca.pcs; px.value=String(i); py.value=String(j); px.onchange=()=>{ const i=parseInt(px.value); if(i===state.pca.pcs[1]){ state.pca.pcs[0]=state.pca.pcs[1]; state.pca.pcs[1]=(i+1)%m; py.value=String(state.pca.pcs[1]); } else state.pca.pcs[0]=i; fitScale(); drawAll(); }; py.onchange=()=>{ const j=parseInt(py.value); if(j===state.pca.pcs[0]){ state.pca.pcs[1]=(j+1)%m; py.value=String(state.pca.pcs[1]); } else state.pca.pcs[1]=j; fitScale(); drawAll(); } }

    // ===== Rendering =====
    const plot=el('plotCanvas'); const metr=el('metricCanvas'); const tree=el('treeCanvas'); const pctx=plot.getContext('2d'); const mctx=metr.getContext('2d'); const tctx=tree.getContext('2d');
    function mapX(x){const {xmin,xmax}=state.scale; return (x-xmin)/(xmax-xmin)*(plot.width-40)+20}
    function mapY(y){const {ymin,ymax}=state.scale; return (1-(y-ymin)/(ymax-ymin))*(plot.height-40)+20}
    function clearCanvas(c){const ctx=c.getContext('2d'); ctx.clearRect(0,0,c.width,c.height);}
    function drawGrid(ctx,w,h){ctx.save();ctx.strokeStyle='rgba(255,255,255,.06)';ctx.lineWidth=1;for(let x=40;x<w;x+=40){ctx.beginPath();ctx.moveTo(x,0);ctx.lineTo(x,h);ctx.stroke();}for(let y=40;y<h;y+=40){ctx.beginPath();ctx.moveTo(0,y);ctx.lineTo(w,y);ctx.stroke();}ctx.restore();}
    function resizeCanvases(){const dpr=window.devicePixelRatio||1; [plot,metr,tree].forEach(c=>{const rect=c.getBoundingClientRect(); c.width=Math.floor(rect.width*dpr); c.height=Math.floor(rect.height*dpr); c.getContext('2d').setTransform(dpr,0,0,dpr,0,0);});}

    function fitScale(){ const [i,j]=state.pca.pcs; const [xi,xj]=projectToPCs(i,j); const pad=.2; const xmin=Math.min(...xi), xmax=Math.max(...xi); const ymin=Math.min(...xj), ymax=Math.max(...xj); const xr=xmax-xmin, yr=ymax-ymin; state._proj={xi,xj}; state.scale={xmin:xmin-xr*pad,xmax:xmax+xr*pad,ymin:ymin-yr*pad,ymax:ymax+yr*pad}; }

    function drawAll(){ resizeCanvases(); drawSurface(); drawPoints(); drawSplits(); drawMetrics(); drawTreeCanvas(); updateEquation(); }

    function drawSurface(){ const w=plot.width/(window.devicePixelRatio||1), h=plot.height/(window.devicePixelRatio||1); clearCanvas(plot); drawGrid(pctx,w,h); pctx.strokeStyle='rgba(255,255,255,.25)'; pctx.lineWidth=1.5; pctx.strokeRect(20,20,w-40,h-40); const cols=140, rows=100; const {xmin,xmax,ymin,ymax}=state.scale; const dx=(xmax-xmin)/cols, dy=(ymax-ymin)/rows; for(let r=0;r<rows;r++){ for(let c=0;c<cols;c++){ const px=xmin+(c+0.5)*dx; const py=ymin+(r+0.5)*dy; // traverse tree but for splits on original features; cannot determine from (px,py) uniquely; we stop when encountering a split and color by majority in that node to convey region.
          let n=state.model.root; while(n && n.leaf===false){ // stop at first split to shade region; detailed line drawing handled separately
            break; }
          const counts = n ? n.counts : Array(state.K).fill(0); let maxC=0, arg=0; for(let k=0;k<state.K;k++){ if(counts[k]>maxC){maxC=counts[k]; arg=k;} }
          pctx.fillStyle = hexToRgba(COLORS[arg%COLORS.length],0.08);
          pctx.fillRect(mapX(px)-0.5, mapY(py)-0.5, 2, 2);
      }} }

    function drawPoints(){ const N=state.y.length; const xi=state._proj.xi, xj=state._proj.xj; for(let i=0;i<N;i++){ pctx.beginPath(); pctx.arc(mapX(xi[i]), mapY(xj[i]), 2.2, 0, Math.PI*2); pctx.fillStyle = COLORS[state.y[i]%COLORS.length]; pctx.fill(); } }

    function drawSplitLabel(txt,x,y){ pctx.save(); pctx.font='12px ui-monospace,Consolas,Menlo,monospace'; const pad=4; const w=pctx.measureText(txt).width+pad*2; const h=18; pctx.fillStyle='rgba(15,23,42,0.85)'; pctx.strokeStyle='rgba(255,255,255,0.25)'; pctx.lineWidth=1; pctx.beginPath(); pctx.roundRect ? pctx.roundRect(x-w/2,y-h/2,w,h,6) : pctx.rect(x-w/2,y-h/2,w,h); pctx.fill(); pctx.stroke(); pctx.fillStyle='#e5e7eb'; pctx.textAlign='center'; pctx.textBaseline='middle'; pctx.fillText(txt,x,y+0.5); pctx.restore(); }

    function drawSplits(){ // In PCA space, original axis-aligned splits are not axis-aligned; we show node regions and labels only.
      pctx.save(); function drawNode(n){ if(!n) return; if(n===state.current){ const r=n.rect; pctx.fillStyle='rgba(251,191,36,0.10)'; pctx.fillRect(mapX(r.xmin), mapY(r.ymax), mapX(r.xmax)-mapX(r.xmin), mapY(r.ymin)-mapY(r.ymax)); }
        if(n.leaf===false){ const r=n.rect; pctx.setLineDash([5,5]); pctx.strokeStyle='#22c55e'; pctx.lineWidth=1.5; pctx.strokeRect(mapX(r.xmin), mapY(r.ymax), mapX(r.xmax)-mapX(r.xmin), mapY(r.ymin)-mapY(r.ymax)); pctx.setLineDash([]); drawSplitLabel(`split on x${n.feat} @ ${n.thr.toFixed(3)}`, (mapX(r.xmin)+mapX(r.xmax))/2, (mapY(r.ymax)+mapY(r.ymin))/2 ); drawNode(n.left); drawNode(n.right); }
      }
      drawNode(state.model.root); pctx.restore(); }

    function drawMetrics(){ clearCanvas(metr); const w=metr.width/(window.devicePixelRatio||1), h=metr.height/(window.devicePixelRatio||1); drawGrid(mctx,w,h); mctx.strokeStyle='rgba(255,255,255,.25)'; mctx.lineWidth=1.5; mctx.strokeRect(20,20,w-40,h-40); if(state.metrics.history.length===0) return; const padL=30,padR=20,padT=20,padB=30; const xmin=0,xmax=state.metrics.history.length-1; const ymin=Math.min(...state.metrics.history),ymax=1; const mapLX=(x)=>padL+(x-xmin)/Math.max(1,(xmax-xmin))*(w-padL-padR); const mapLY=(y)=>padT+(1-(y-ymin)/Math.max(1e-9,(ymax-ymin)))*(h-padT-padB); mctx.strokeStyle='#22c55e'; mctx.lineWidth=2; mctx.beginPath(); mctx.moveTo(mapLX(0),mapLY(state.metrics.history[0])); for(let i=1;i<state.metrics.history.length;i++){ mctx.lineTo(mapLX(i),mapLY(state.metrics.history[i])); } mctx.stroke(); mctx.fillStyle='rgba(229,231,235,.9)'; mctx.font='12px ui-sans-serif,system-ui'; mctx.fillText('Step',w/2-14,h-8); mctx.save(); mctx.translate(8,h/2+20); mctx.rotate(-Math.PI/2); mctx.fillText('Accuracy',0,0); mctx.restore(); }

    // ===== Tree canvas =====
    function layoutTree(root){ const levels=[]; const leaves=[]; (function collect(n,d){ if(!n) return; n.depth=d; if(n.leaf){ leaves.push(n); } else { collect(n.left,d+1); collect(n.right,d+1); } if(!levels[d]) levels[d]=[]; levels[d].push(n); })(root,0); const W=tree.width/(window.devicePixelRatio||1), H=tree.height/(window.devicePixelRatio||1); const padX=40, padY=30; const cols=Math.max(1,leaves.length); const colStep=(W-2*padX)/Math.max(1,cols-1); let order=0; (function setX(n){ if(!n) return; if(n.leaf){ n._tx=padX + order*colStep; order++; } else { setX(n.left); setX(n.right); n._tx = n.left && n.right ? (n.left._tx + n.right._tx)/2 : (n.left? n.left._tx : (n.right? n.right._tx : padX)); } })(root); (function setY(n){ if(!n) return; n._ty = padY + n.depth * ((H-2*padY)/Math.max(1,(levels.length-1))); setY(n.left); setY(n.right); })(root); }

    function drawTreeCanvas(){ const W=tree.width/(window.devicePixelRatio||1), H=tree.height/(window.devicePixelRatio||1); clearCanvas(tree); tctx.fillStyle='#0a1120'; tctx.fillRect(0,0,W,H); tctx.strokeStyle='rgba(255,255,255,.25)'; tctx.strokeRect(0,0,W,H); const root=state.model.root; if(!root) return; layoutTree(root); tctx.lineWidth=1.5; function edge(a,b,label){ tctx.strokeStyle='rgba(255,255,255,.25)'; tctx.beginPath(); tctx.moveTo(a._tx,a._ty+16); tctx.lineTo(b._tx,b._ty-16); tctx.stroke(); if(label){ drawTreeEdgeLabel(label,(a._tx+b._tx)/2,(a._ty+b._ty)/2); } } (function walkEdges(n){ if(!n || n.leaf) return; const lLabel = `≤ ${n.thr.toFixed(3)}`; const rLabel = `> ${n.thr.toFixed(3)}`; edge(n,n.left,lLabel); edge(n,n.right,rLabel); walkEdges(n.left); walkEdges(n.right); })(root); function nodeBox(n){ const top = n.leaf? `leaf • pred=${n.pred}` : `x${n.feat} ≤ ${n.thr.toFixed(3)}`; const countsTxt='['+n.counts.map((c,i)=>`${i}:${c}`).join(' ')+']'; const bot = `n=${n.idx.length} | ${countsTxt}`; const w = Math.max(140, 12* Math.max(top.length, bot.length)/1.6); const h = 48; const x=n._tx - w/2, y=n._ty - h/2; tctx.fillStyle = n===state.current? 'rgba(251,191,36,0.12)' : 'rgba(255,255,255,0.04)'; tctx.strokeStyle='rgba(255,255,255,0.18)'; tctx.lineWidth=1; tctx.beginPath(); tctx.roundRect ? tctx.roundRect(x,y,w,h,10) : tctx.rect(x,y,w,h); tctx.fill(); tctx.stroke(); tctx.fillStyle='#e5e7eb'; tctx.font='12px ui-monospace,Consolas,Menlo,monospace'; tctx.textBaseline='middle'; tctx.textAlign='center'; tctx.fillText(top, n._tx, n._ty-10); tctx.fillStyle='var(--muted)'; tctx.fillText(bot, n._tx, n._ty+10); } (function walkNodes(n){ if(!n) return; nodeBox(n); walkNodes(n.left); walkNodes(n.right); })(root); }
    function drawTreeEdgeLabel(txt,x,y){ tctx.save(); tctx.font='11px ui-monospace,Consolas,Menlo,monospace'; const pad=4; const w=tctx.measureText(txt).width+pad*2; const h=16; tctx.fillStyle='rgba(15,23,42,0.85)'; tctx.strokeStyle='rgba(255,255,255,0.25)'; tctx.beginPath(); tctx.roundRect ? tctx.roundRect(x-w/2,y-h/2,w,h,6) : tctx.rect(x-w/2,y-h/2,w,h); tctx.fill(); tctx.stroke(); tctx.fillStyle='#e5e7eb'; tctx.textAlign='center'; tctx.textBaseline='middle'; tctx.fillText(txt,x,y+0.5); tctx.restore(); }

    // ===== Equation / Stats =====
    function updateEquation(){ const crit=state.tree.criterion==='gini'? 'Gini':'Entropy'; const [i,j]=state.pca.pcs; el('equation').textContent=`criterion = ${crit} (K=${state.K}), D=${state.D}, plot PCs=(PC${i+1}, PC${j+1}) — Gain = Imp(P) − (nL/n)·Imp(L) − (nR/n)·Imp(R)`; }
    function updateStats(){ const nodes=state.model.nodes.length; let depth=0; (function walk(n){ if(!n) return; depth=Math.max(depth,n.depth); if(n.leaf===false){ walk(n.left); walk(n.right);} })(state.model.root); const acc=accuracy(); state.metrics.acc=acc; el('statNodes').textContent=String(nodes); el('statDepth').textContent=String(depth); el('statAcc').textContent=(isFinite(acc)?acc.toFixed(3):'—'); }

    // ===== Calculations UI =====
    function renderTable(rows){ const header='feat\t thr\t nL\t nR\t Imp(P)\t Imp(L)\t Imp(R)\t Gain\n'+'-'.repeat(100)+'\n'; const pad=(v)=> (Number.isFinite(v)?v.toFixed(6):'—').padStart(8,' '); const lines=rows.slice(0,80).map(r=>`${r.feat.padEnd(3,' ')}\t${pad(r.thr)}\t${String(r.nL).padStart(4)}\t${String(r.nR).padStart(4)}\t${pad(r.impP)}\t${pad(r.impL)}\t${pad(r.impR)}\t${pad(r.gain)}`); el('tableBox').textContent=header+lines.join('\n'); }
    function renderPhase(p,info){ const titles=['Phase 1 — Evaluate candidate splits','Phase 2 — Best split calculations','Phase 3 — Apply split','Phase 4 — Update metrics']; el('phaseTitle').textContent=titles[p]; const f=(x)=>Number.isFinite(x)?x.toFixed(6):'—'; if(p===0){ const {node,best}=info; if(!best){ el('calcMain').textContent='No valid split (minLeaf/depth/purity reached).'; el('tableBox').textContent='—'; return; } const head=`Node depth=${node.depth}, size=${node.idx.length}\nParent Impurity = ${f(best.impP)}\nCandidates (top by gain):`; el('calcMain').textContent=head; renderTable(best.rows); } if(p===1){ const {best}=info; if(!best){ el('calcMain').textContent='—'; return; } const n=best.nL+best.nR; el('calcMain').textContent=`Best: feat=x${best.feat}, thr=${f(best.thr)}\nImp(P)=${f(best.impP)}\nImp(L)=${f(best.impL)} with nL=${best.nL}\nImp(R)=${f(best.impR)} with nR=${best.nR}\nGain = Imp(P) − (nL/n)·Imp(L) − (nR/n)·Imp(R) = ${f(best.gain)}`; } if(p===2){ el('calcMain').textContent='Applied split: new left/right children added to queue.'; } if(p===3){ el('calcMain').textContent=`Accuracy snapshot = ${f(info.acc)}`; } }

    function updatePhaseUI(){ const badge=el('phaseBadge'); if(!state.training.running && el('modeClassic').classList.contains('active')){ badge.textContent='Classic mode'; badge.className='badge phase-0'; el('btnNextPhase').style.display='none'; el('calcCard').style.display='none'; } else if(el('modePhased').classList.contains('active')){ const titles=['Phase 1 — Evaluate candidate splits','Phase 2 — Best split calculations','Phase 3 — Apply split','Phase 4 — Update metrics']; badge.textContent=titles[state.training.phase]; badge.className='badge phase-'+state.training.phase; el('btnNextPhase').style.display='inline-block'; el('calcCard').style.display='block'; } else { badge.textContent='Classic mode'; badge.className='badge phase-0'; el('btnNextPhase').style.display='none'; el('calcCard').style.display='none'; } updateEquation(); drawAll(); }

    // ===== Events =====
    function setMode(mode){ el('modeClassic').classList.toggle('active', mode==='classic'); el('modePhased').classList.toggle('active', mode==='phased'); state.training.phase=0; state.training.running=false; updatePhaseUI(); }
    el('modeClassic').addEventListener('click',()=>setMode('classic'));
    el('modePhased').addEventListener('click',()=>setMode('phased'));

    el('btnRegenerate').addEventListener('click',()=>{ generateData(Math.round(parseFloat(el('points').value))); });
    el('btnShuffle').addEventListener('click',()=>{ state.seed=Math.floor(Math.random()*1e9); generateData(Math.round(parseFloat(el('points').value))); });
    el('btnReset').addEventListener('click',()=>{ resetTree(); drawAll(); el('calcMain').textContent='—'; el('tableBox').textContent='—'; state.metrics.history=[]; });

    el('btnStart').addEventListener('click',(e)=>{ const phased = el('modePhased').classList.contains('active'); state.training.running=!state.training.running; e.target.textContent=state.training.running?'Pause':'Start'; if(state.training.running){ (function loop(){ if(!state.training.running) return; if(phased){ nextPhase(); } else { classicStep(); } requestAnimationFrame(loop); })(); }});
    el('btnStep').addEventListener('click',()=>{ const phased = el('modePhased').classList.contains('active'); if(!state.training.running){ if(phased){ nextPhase(); } else { classicStep(); } }});
    el('btnNextPhase').addEventListener('click',()=>{ if(!state.training.running) nextPhase(); });

    // ===== Bind controls =====
    bindPair(el('points'),el('pointsNum'),v=>{ el('kThresh').max=Math.min(160,Math.max(5,Math.round(v/10))); el('kThreshNum').max=el('kThresh').max;});
    bindPair(el('noise'),el('noiseNum'),v=>{ state.gen.sigma=v; });
    bindPair(el('sep'),el('sepNum'),v=>{ state.gen.sep=v; });
    bindPair(el('maxDepth'),el('maxDepthNum'),v=>{ state.tree.maxDepth=Math.round(v); });
    bindPair(el('minLeaf'),el('minLeafNum'),v=>{ state.tree.minLeaf=Math.round(v); });
    el('criterion').addEventListener('change',e=>{ state.tree.criterion=e.target.value; updateEquation(); });
    bindPair(el('kThresh'),el('kThreshNum'),v=>{ state.tree.kThresh=Math.round(v); });
    bindPair(el('dims'),el('dimsNum'),v=>{ state.D=Math.round(v); generateData(Math.round(parseFloat(el('points').value))); });
    bindPair(el('classes'),el('classesNum'),v=>{ state.K=Math.round(v); generateData(Math.round(parseFloat(el('points').value))); });
    el('btnReproject').addEventListener('click',()=>{ computePCA(); buildPCSelectors(); fitScale(); drawAll(); });

    // ===== Utilities =====
    function hexToRgba(hex,a){ const h=hex.replace('#',''); const bigint=parseInt(h,16); const r=(bigint>>16)&255, g=(bigint>>8)&255, b=bigint&255; return `rgba(${r},${g},${b},${a})`; }

    // ===== Init =====
    window.addEventListener('resize',()=>{ drawAll(); });
    generateData(1200);
    setMode('classic');
    updateEquation();
  </script>
</body>
</html>
