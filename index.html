<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>ML Visual Playground</title>
  <link rel="stylesheet" href="css/styles.css">
</head>
<body>
  <div class="app-shell">
    <header class="topbar">
      <div class="brand">ML Visual Playground</div>
      <nav class="nav">
        <a href="gradient-descent.html" class="btn">Gradient Descent</a>
        <a href="neural-network.html" class="btn">Neural Network</a>
      </nav>
    </header>

    <main class="content">
      <section class="hero">
        <h1>Interactive ML visualizations</h1>
        <p>Switch between demos using the buttons above. Each demo has interactive controls, live math, and an iteration log.</p>
        <div class="cards">
          <a class="card" href="gradient-descent.html">
            <h3>Gradient Descent</h3>
            <p>Visualize gradient descent on simple functions with step-by-step controls.</p>
          </a>
          <a class="card" href="neural-network.html">
            <h3>Neural Network</h3>
            <p>Forward-pass visualization for a small dense network.</p>
          </a>
        </div>
      </section>

      <section class="footer-note">
        <p>Template created for visualizing ML concepts. Edit files in <code>js/</code> and <code>css/</code>.</p>
      </section>
    </main>

    <footer class="footer">Made for learning · Minimal demo</footer>
  </div>

  <script src="js/main.js"></script>
</body>
</html>
