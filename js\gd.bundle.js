// gd.bundle.js - small bundle shimming the module for simple use
/* This file simply inlines gradient.js logic so the browser can load it without modules. */
(function(){
  // copy of gradient.js content
  // function f(x) = x^2 + 5
  function f(x){return x*x + 5}
  function df(x){return 2*x}

  const container = document.getElementById('gd-canvas');
  const ctx = (function(){
    const c = document.createElement('canvas');
    c.width = container.clientWidth - 20 || 760;
    c.height = 520;
    container.appendChild(c);
    return c.getContext('2d');
  })();

  const lrEl = document.getElementById('lr');
  const lrVal = document.getElementById('lr-val');
  const startEl = document.getElementById('start');
  const startVal = document.getElementById('start-val');
  const itersEl = document.getElementById('iters');
  const itersVal = document.getElementById('iters-val');
  const speedEl = document.getElementById('speed');
  const speedVal = document.getElementById('speed-val');
  const mathBox = document.getElementById('math');
  const iterLog = document.getElementById('iter-log');
  const playBtn = document.getElementById('play');
  const stepBtn = document.getElementById('step');
  const resetBtn = document.getElementById('reset');

  function drawAxes(){
    const w=ctx.canvas.width;const h=ctx.canvas.height;
    ctx.clearRect(0,0,w,h);
    ctx.fillStyle='#071521';ctx.fillRect(0,0,w,h);
    ctx.strokeStyle='rgba(255,255,255,0.03)';ctx.beginPath();
    for(let i=0;i<10;i++){const x=i*(w/10);ctx.moveTo(x,0);ctx.lineTo(x,h)}
    for(let j=0;j<10;j++){const y=j*(h/10);ctx.moveTo(0,y);ctx.lineTo(w,y)}
    ctx.stroke();
  }

  function drawFunction(){
    const w=ctx.canvas.width;const h=ctx.canvas.height;
    ctx.strokeStyle='#38c6ff';ctx.lineWidth=2;ctx.beginPath();
    for(let i=0;i<=w;i++){
      const x = (i/w)*20 - 10;
      const y = f(x);
      const py = h - (y/110)*h;
      if(i===0) ctx.moveTo(i,py); else ctx.lineTo(i,py);
    }
    ctx.stroke();
  }

  function drawPoint(x,color='lime',size=8){
    const w=ctx.canvas.width;const h=ctx.canvas.height;
    const px = ((x+10)/20)*w;
    const py = h - (f(x)/110)*h;
    ctx.fillStyle=color;ctx.beginPath();ctx.arc(px,py,size,0,Math.PI*2);ctx.fill();
  }

  function drawTangentAt(x,color='orange'){
    const w=ctx.canvas.width;const h=ctx.canvas.height;
    const slope = df(x);
    // pick two x positions around x
    const x0 = x - 4; const x1 = x + 4;
    const y0 = f(x0); const y1 = f(x1);
    // tangent: y_t = f(x) + f'(x)*(x_t - x)
    const ty0 = f(x) + slope*(x0 - x);
    const ty1 = f(x) + slope*(x1 - x);
    const px0 = ((x0+10)/20)*w; const py0 = h - (ty0/110)*h;
    const px1 = ((x1+10)/20)*w; const py1 = h - (ty1/110)*h;
    ctx.save();
    ctx.setLineDash([8,6]);
    ctx.strokeStyle = color; ctx.lineWidth = 2; ctx.beginPath();
    ctx.moveTo(px0,py0); ctx.lineTo(px1,py1); ctx.stroke();
    ctx.restore();
  }

  function drawNextPoint(xNext){
    // draw smaller point and arrow from current x to next
    const w=ctx.canvas.width;const h=ctx.canvas.height;
    const pxCur = ((state.x+10)/20)*w;
    const pyCur = h - (f(state.x)/110)*h;
    const pxNext = ((xNext+10)/20)*w;
    const pyNext = h - (f(xNext)/110)*h;
    // arrow
    ctx.save(); ctx.strokeStyle='#9b7bff'; ctx.fillStyle='#9b7bff'; ctx.lineWidth=2;
    ctx.beginPath(); ctx.moveTo(pxCur,pyCur); ctx.lineTo(pxNext,pyNext); ctx.stroke();
    // arrowhead
    const ang = Math.atan2(pyNext-pyCur, pxNext-pxCur);
    const ah = 8;
    ctx.beginPath(); ctx.moveTo(pxNext,pyNext);
    ctx.lineTo(pxNext - ah*Math.cos(ang-0.4), pyNext - ah*Math.sin(ang-0.4));
    ctx.lineTo(pxNext - ah*Math.cos(ang+0.4), pyNext - ah*Math.sin(ang+0.4));
    ctx.closePath(); ctx.fill(); ctx.restore();
    // small next point
    ctx.beginPath(); ctx.fillStyle='#68e0ff'; ctx.arc(pxNext,pyNext,6,0,Math.PI*2); ctx.fill();
  }

  let state = {x:parseFloat(startEl.value), it:0, maxIt:parseInt(itersEl.value), running:false};
    // animation state for smooth transitions
    let anim = {px:0, py:0, nextPx:0, nextPy:0, arrow:{x1:0,y1:0,x2:0,y2:0}, initialized:false};
    function lerp(a,b,t){ return a + (b-a)*t }
    function rafLoop(){ drawAxes(); drawFunction();
      // compute targets
      const x = state.x; const y = f(x); const targetPx = ((x+10)/20)*ctx.canvas.width; const targetPy = ctx.canvas.height - (f(x)/110)*ctx.canvas.height;
      if(!anim.initialized){ anim.px=targetPx; anim.py=targetPy; anim.initialized=true; }
      // ease towards target
      anim.px = lerp(anim.px, targetPx, 0.22); anim.py = lerp(anim.py, targetPy, 0.22);
      // draw current point
      ctx.beginPath(); ctx.fillStyle='#27f58c'; ctx.arc(anim.px, anim.py, 9, 0, Math.PI*2); ctx.fill();
      // tangent (draw using current x for math but draw full path)
      try{
        const eta = parseFloat(lrEl.value);
        const grad = df(state.x);
        const xNext = state.x - eta*grad;
        drawTangentAt(state.x,'#ffb86b');
        // animate next point
        const targetNx = ((xNext+10)/20)*ctx.canvas.width; const targetNy = ctx.canvas.height - (f(xNext)/110)*ctx.canvas.height;
        anim.nextPx = lerp(anim.nextPx || targetNx, targetNx, 0.22); anim.nextPy = lerp(anim.nextPy || targetNy, targetNy, 0.22);
        // draw arrow from current animated pos to animated next
        ctx.save(); ctx.strokeStyle='#9b7bff'; ctx.fillStyle='#9b7bff'; ctx.lineWidth=2; ctx.beginPath(); ctx.moveTo(anim.px,anim.py); ctx.lineTo(anim.nextPx,anim.nextPy); ctx.stroke();
        const ang = Math.atan2(anim.nextPy-anim.py, anim.nextPx-anim.px); const ah=8;
        ctx.beginPath(); ctx.moveTo(anim.nextPx,anim.nextPy);
        ctx.lineTo(anim.nextPx - ah*Math.cos(ang-0.4), anim.nextPy - ah*Math.sin(ang-0.4));
        ctx.lineTo(anim.nextPx - ah*Math.cos(ang+0.4), anim.nextPy - ah*Math.sin(ang+0.4));
        ctx.closePath(); ctx.fill(); ctx.restore();
        // small next point
        ctx.beginPath(); ctx.fillStyle='#68e0ff'; ctx.arc(anim.nextPx,anim.nextPy,6,0,Math.PI*2); ctx.fill();
      }catch(e){}
      requestAnimationFrame(rafLoop);
    }

  // render is now handled by rafLoop for smooth animation
  function render(){ /* kept for compatibility */ }

  function updateMath(){
    const eta = parseFloat(lrEl.value);
    const grad = df(state.x);
    const delta = eta * grad;
    mathBox.textContent = `f(x)=x^2+5, ∇f(x)=2x\n∇f(x_t) = ${grad.toFixed(3)}\nΔ=η∇f = ${delta.toFixed(3)}\n x_{t+1} = ${state.x.toFixed(3)} - ${delta.toFixed(3)} = ${(state.x - delta).toFixed(3)}`;
  }

  function logIteration(){
    const row = document.createElement('div');
    const grad = df(state.x); const eta = parseFloat(lrEl.value);
    row.textContent = `${state.it}\t ${state.x.toFixed(3)}\t ${grad.toFixed(3)}\t ${(-eta*grad).toFixed(3)}\t ${f(state.x).toFixed(3)}`;
    iterLog.prepend(row);
  }

  function step(){
    if(state.it >= state.maxIt) return; state.it++;
    logIteration();
    const eta = parseFloat(lrEl.value);
    const grad = df(state.x);
    state.x = state.x - eta * grad;
    updateMath();
  }

  function reset(){
    state.x = parseFloat(startEl.value); state.it=0; state.maxIt=parseInt(itersEl.value); iterLog.innerHTML=''; updateMath(); render();
  }

  lrEl.addEventListener('input',()=>{lrVal.textContent = lrEl.value; updateMath();});
  startEl.addEventListener('input',()=>{startVal.textContent = startEl.value;});
  itersEl.addEventListener('input',()=>{itersVal.textContent = itersEl.value; state.maxIt = parseInt(itersEl.value);});
  speedEl.addEventListener('input',()=>{speedVal.textContent = speedEl.value + 'x'});
  playBtn.addEventListener('click',()=>{
    if(state.running){state.running=false;playBtn.textContent='Play';return}
    state.running=true;playBtn.textContent='Pause';
    const run = ()=>{
      if(!state.running) return; if(state.it>=state.maxIt){state.running=false;playBtn.textContent='Play';return}
      step();
      setTimeout(run, 600 / parseFloat(speedEl.value));
    };
    run();
  });
  stepBtn.addEventListener('click',step);
  resetBtn.addEventListener('click',reset);

  updateMath();render();
    // start animation
    requestAnimationFrame(rafLoop);
})();
