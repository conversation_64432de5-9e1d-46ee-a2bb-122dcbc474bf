<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>CNN Convolution Visualizer</title>
<style>
  :root {
    --bg: #0f172a;        /* slate-900 */
    --panel: #111827;     /* gray-900 */
    --muted: #94a3b8;     /* slate-400 */
    --text: #e5e7eb;      /* gray-200 */
    --accent: #22d3ee;    /* cyan-400 */
    --accent-2: #a78bfa;  /* violet-400 */
    --warn: #f59e0b;      /* amber-500 */
    --good: #34d399;      /* emerald-400 */
    --bad: #fb7185;       /* rose-400 */
    --grid-border: #1f2937; /* gray-800 */
  }
  * { box-sizing: border-box; }
  html, body { height: 100%; }
  body {
    margin: 0; font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, <PERSON>l, "Apple Color Emoji", "Segoe UI Emoji";
    background: radial-gradient(1200px 600px at 10% -10%, rgba(34,211,238,0.08), transparent 70%),
                radial-gradient(1200px 600px at 110% 10%, rgba(167,139,250,0.08), transparent 70%), var(--bg);
    color: var(--text);
  }
  header {
    display:flex; gap:16px; align-items:center; justify-content:space-between;
    padding:16px 20px; border-bottom:1px solid #1f2937; position:sticky; top:0; background:rgba(15,23,42,0.9); backdrop-filter: blur(8px);
  }
  header h1 { font-size: 18px; margin: 0; letter-spacing: .2px; }
  header .sub { color: var(--muted); font-size: 12px; }
  .wrap {
    display:grid; grid-template-columns: 360px 1fr; gap: 18px; padding: 18px; height: calc(100% - 62px);
  }
  .card {
    background: linear-gradient(180deg, rgba(255,255,255,0.03), rgba(255,255,255,0.01));
    border: 1px solid #1f2937; border-radius: 16px; padding: 14px; box-shadow: 0 10px 30px rgba(0,0,0,0.25);
  }
  .controls label { display:block; font-size:12px; color: var(--muted); margin: 10px 0 6px; }
  .controls input[type="number"], .controls select, .controls input[type="text"] {
    width: 100%; background: #0b1220; color: var(--text); border: 1px solid #293244; border-radius: 10px; padding: 8px 10px; outline: none;
  }
  .controls .row { display:grid; grid-template-columns: 1fr 1fr; gap: 10px; }
  .controls .grid3 { display:grid; grid-template-columns: repeat(3, 1fr); gap: 6px; }
  .controls .grid3 input { text-align:center; }
  .buttonbar { display:flex; gap:8px; flex-wrap: wrap; margin-top: 10px; }
  button {
    background: #0b1220; color: var(--text); border:1px solid #293244; border-radius: 999px; padding: 8px 12px; cursor: pointer;
    transition: transform .08s ease, background .2s ease, border-color .2s ease; font-weight:600; font-size: 12px; letter-spacing:.2px;
  }
  button:hover { background:#0f172a; border-color:#364055; }
  button:active { transform: scale(0.98); }
  button.primary { background: linear-gradient(90deg, rgba(34,211,238,0.18), rgba(167,139,250,0.18)); border-color: #334155; }
  button.warn { border-color: #3b2c12; background: #1a1305; }
  .speed { display:flex; align-items:center; gap:8px; }
  input[type="range"] { width:100%; }

  .stage { display:grid; grid-template-columns: 1fr 420px; gap: 18px; height: 100%; align-content: start; }
  .grids { display:grid; grid-template-columns: 1fr 1fr; gap: 18px; align-content:start; }
  .grid-title { display:flex; align-items:center; justify-content:space-between; margin-bottom:6px; }
  .grid-title h3 { margin:0; font-size:14px; font-weight:700; white-space:nowrap; }
  .grid-title small { color: var(--muted); }

  .matrix { display:grid; gap: 1px; background: var(--grid-border); border:1px solid var(--grid-border); border-radius:10px; overflow:hidden; }
  .cell { background:#0b1220; text-align:center; padding: clamp(6px, 1.2vw, 10px) 6px; font-size: clamp(10px, 1.1vw, 12px); color: var(--text); position: relative; user-select: none; transition: background-color .15s ease, outline-color .15s ease; }
  .cell.editable { cursor: pointer; }
  .cell.highlight { outline: 2px solid var(--accent); outline-offset:-2px; z-index: 1; }
  .legend { display:flex; gap:10px; flex-wrap:wrap; font-size:12px; color:var(--muted); }
  .legend .dot { width:10px; height:10px; border-radius:50%; display:inline-block; margin-right:6px; vertical-align:middle; }
  .legend span { display:flex; align-items:center; gap:6px; }
  .note { color: var(--muted); font-size:12px; }
  .calc { display:grid; gap:12px; max-height: 78vh; overflow:auto; }
  .calc .row { display:grid; grid-template-columns: 1fr 20px 1fr 20px 1fr; gap:8px; align-items:center; }
  .calc .arrow { text-align:center; color: var(--muted); }
  .calc .sum { font-weight:700; }
  .badge { display:inline-block; padding:2px 8px; border-radius:999px; background:#0b1220; border:1px solid #253046; color:var(--muted); font-size:11px; }
  .pill { display:inline-flex; align-items:center; gap:8px; padding:6px 10px; border-radius:999px; background:#0b1220; border:1px dashed #2b3550; }
  .desc { color: var(--muted); font-size: 12px; }
  .footer { margin-top: 6px; display:flex; flex-wrap:wrap; gap:8px; align-items:center; justify-content:space-between; }
  .kbd { background:#0b1220; border:1px solid #263149; border-bottom-width:3px; padding:2px 6px; border-radius:6px; font-weight:700; font-size:11px; }
  .spacer { height:6px; }

  /* Sidebar behavior */
  .controls { position: sticky; top: 72px; max-height: calc(100vh - 96px); overflow: auto; }
  .controls .buttonbar, .controls .speed, .controls .footer { position: relative; }
  .controls input[type="file"]{ width:100%; }

  /* Scrolling for tall grids */
  .grids .matrix { max-height: 58vh; overflow: auto; }

  @media (max-width: 1280px){
    .stage { grid-template-columns: 1fr; }
    .grids .matrix { max-height: none; }
    .calc { max-height: none; }
  }
</style>
</head>
<body>
  <header>
    <div>
      <h1>CNN Convolution Visualizer</h1>
      <div class="sub">Animate the sliding window • See multiply-accumulate math • Compare filters & effects</div>
    </div>
    <div class="legend">
      <span><i class="dot" style="background:var(--accent)"></i>Active window</span>
      <span><i class="dot" style="background:var(--accent-2)"></i>Kernel cell</span>
    </div>
  </header>

  <div class="wrap">
    <!-- Controls -->
    <aside class="card controls" id="controls">
      <div class="row">
        <div>
          <label>Input size (NxN)</label>
          <input id="inpSize" type="number" min="4" max="32" value="12" />
        </div>
        <div>
          <label>Kernel size</label>
          <select id="kernelSize">
            <option value="3" selected>3×3</option>
            <option value="5">5×5</option>
          </select>
        </div>
      </div>

      <label>Input pattern</label>
      <select id="inputPattern">
        <option value="none" selected>Manual (click to edit)</option>
        <option value="gradient">Gradient</option>
        <option value="checker">Checkerboard</option>
        <option value="vstripes">Vertical stripes</option>
        <option value="hstripes">Horizontal stripes</option>
        <option value="diagonal">Main diagonal</option>
        <option value="center_dot">Centered dot</option>
        <option value="square">Centered square</option>
        <option value="circle">Centered circle</option>
        <option value="random">Random noise</option>
        <option value="zeros">All zeros</option>
      </select>
      <label style="margin-top:6px">Or load image</label>
      <input id="imgUpload" type="file" accept="image/*" />
      <small class="note">Images are resized to N×N and converted to grayscale.</small>

      <label>Preset filter</label>
      <select id="preset">
        <option value="identity">Identity</option>
        <option value="box3">Box Blur (3×3)</option>
        <option value="gauss3">Gaussian Blur (3×3)</option>
        <option value="sharpen">Sharpen</option>
        <option value="edge_h">Edge (Sobel Horizontal)</option>
        <option value="edge_v">Edge (Sobel Vertical)</option>
        <option value="laplace">Laplacian</option>
        <option value="emboss">Emboss</option>
        <option value="custom">Custom…</option>
      </select>
      <div id="presetDesc" class="desc" style="margin-top:6px"></div>

      <div class="spacer"></div>

      <div class="row">
        <div>
          <label>Stride</label>
          <select id="stride">
            <option value="1" selected>1</option>
            <option value="2">2</option>
          </select>
        </div>
        <div>
          <label>Padding</label>
          <select id="padding">
            <option value="same">Same (zero)</option>
            <option value="valid">Valid</option>
          </select>
        </div>
      </div>

      <div class="row">
        <div>
          <label>Bias</label>
          <input id="bias" type="number" step="0.1" value="0" />
        </div>
        <div>
          <label>Activation</label>
          <select id="activation">
            <option value="none" selected>None</option>
            <option value="relu">ReLU</option>
          </select>
        </div>
      </div>

      <label>Kernel weights</label>
      <div id="kernelEditor" class="grid3"></div>
      <small class="note">Tip: choose a preset, then tweak any cell (auto-normalizes blur kernels).</small>

      <div class="buttonbar">
        <button class="primary" id="playBtn">▶ Play</button>
        <button id="stepBtn">Step</button>
        <button id="pauseBtn">Pause</button>
        <button class="warn" id="resetBtn">Reset</button>
      </div>

      <div class="speed" style="margin-top:4px">
        <span class="badge">Speed</span>
        <input id="speed" type="range" min="50" max="800" value="300" />
      </div>

      <div class="buttonbar" style="margin-top:10px">
        <button id="randomInput">Random input</button>
        <button id="gradientInput">Gradient input</button>
        <button id="clearInput">Clear input</button>
      </div>

      <div class="footer">
        <div>
          <span class="pill"><b>Keyboard:</b> <span class="kbd">Space</span> play/pause • <span class="kbd">→</span> step</span>
        </div>
        <div class="pill">Values are 0–255 (grayscale)</div>
      </div>
    </aside>

    <!-- Stage -->
    <section class="stage">
      <div class="card">
        <div class="grids">
          <div>
            <div class="grid-title">
              <h3>Input</h3>
              <small id="inSizeLabel"></small>
            </div>
            <div id="inputGrid" class="matrix"></div>
          </div>
          <div>
            <div class="grid-title">
              <h3>Output</h3>
              <small id="outSizeLabel"></small>
            </div>
            <div id="outputGrid" class="matrix"></div>
          </div>
        </div>
      </div>

      <div class="card calc">
        <div style="display:flex; align-items:center; justify-content:space-between; gap:10px">
          <h3 style="margin:0">Step Math</h3>
          <div class="pill" id="posBadge">(i, j) = (–, –)</div>
        </div>
        <div class="row">
          <div>
            <div class="grid-title"><h3>Window</h3><small id="winLabel"></small></div>
            <div id="winGrid" class="matrix"></div>
          </div>
          <div class="arrow">×</div>
          <div>
            <div class="grid-title"><h3>Kernel</h3><small id="kerLabel"></small></div>
            <div id="kerGrid" class="matrix"></div>
          </div>
          <div class="arrow">=</div>
          <div>
            <div class="grid-title"><h3>Product</h3><small id="prodLabel"></small></div>
            <div id="prodGrid" class="matrix"></div>
          </div>
        </div>
        <div style="display:flex; gap:10px; align-items:center; flex-wrap:wrap">
          <span class="badge">Sum</span>
          <span id="sumVal" class="sum">–</span>
          <span class="badge">+ Bias</span>
          <span id="biasVal">–</span>
          <span class="badge">Activation</span>
          <span id="actVal">–</span>
          <span class="badge">Output(i,j)</span>
          <span id="outVal">–</span>
        </div>
        <div class="desc" id="effectDesc"></div>
      </div>
    </section>
  </div>

<script>
(function(){
  // ---------- Utilities ----------
  const clamp = (v, a, b) => Math.max(a, Math.min(b, v));
  const fmt = v => (Math.round(v * 100) / 100).toString();

  // ----- Heatmap helpers -----
  function lerp(a,b,t){ return a + (b-a)*t; }
  function clamp01(x){ return Math.max(0, Math.min(1, x)); }
  function hsl(h, s, l){ return `hsl(${h} ${s}% ${l}%)`; }
  function seqColor(v, vmin, vmax){ if (!isFinite(v)) return '#0b1220'; const t = clamp01((v - vmin) / (vmax - vmin + 1e-9)); return hsl(210 + 70*t, 85, 14 + 58*t); }
  function divColor(v, vneg, vpos){ if (!isFinite(v)) return '#0b1220'; if (v>0){ const t = clamp01(v / (vpos + 1e-9)); return hsl(0, 80, 18 + 58*t); } if (v<0){ const t = clamp01((-v) / (Math.abs(vneg) + 1e-9)); return hsl(210, 85, 18 + 58*t); } return '#0b1220'; }
  function updateColors(el, values){
    const kind = el.dataset.kind || 'normal';
    const rows = parseInt(el.dataset.rows), cols = parseInt(el.dataset.cols);
    let min=Infinity, max=-Infinity, negMin=0, posMax=0;
    for (let r=0;r<rows;r++){
      for (let c=0;c<cols;c++){
        const v = values[r]?.[c] ?? 0;
        if (v<min) min=v; if (v>max) max=v; if (v<negMin) negMin = v; if (v>posMax) posMax = v;
      }
    }
    for (let r=0;r<rows;r++){
      for (let c=0;c<cols;c++){
        const idx = r*cols + c; const cell = el.children[idx]; const v = values[r]?.[c] ?? 0;
        const bg = (kind==='kernel'||kind==='product'||kind==='output') ? divColor(v, negMin, posMax) : seqColor(v, min, max);
        cell.style.background = bg; cell.setAttribute('data-heat','');
      }
    }
  }

  const filterDescriptions = {
    identity: 'Returns the original image. Useful as a baseline to compare effects.',
    box3: 'Averaging blur that smooths noise but also removes detail (all weights equal).',
    gauss3: 'Gaussian blur gives a softer, more natural smoothing with center emphasis.',
    sharpen: 'Enhances edges by adding a scaled Laplacian to the original (high-boost).',
    edge_h: 'Highlights horizontal edges (changes along vertical direction).',
    edge_v: 'Highlights vertical edges (changes along horizontal direction).',
    laplace: 'Second-derivative operator that detects edges in all directions.',
    emboss: 'Creates a relief effect by emphasizing directional changes with an offset.'
  };

  function makeMatrix(n, m, fill=0) { return Array.from({length:n},()=>Array(m).fill(fill)); }

  function createMatrixEl(rows, cols, {editable=false, kind='normal'}={}) {
    const el = document.createElement('div');
    el.className = 'matrix';
    el.style.gridTemplateColumns = `repeat(${cols}, minmax(22px, 1fr))`;
    el.style.gridTemplateRows = `repeat(${rows}, minmax(22px, 1fr))`;
    el.dataset.rows = rows; el.dataset.cols = cols; el.dataset.kind = kind;
    for (let r=0;r<rows;r++){
      for (let c=0;c<cols;c++){
        const cell = document.createElement('div');
        cell.className = 'cell' + (editable?' editable':'');
        cell.dataset.r = r; cell.dataset.c = c;
        cell.textContent = '0';
        el.appendChild(cell);
      }
    }
    return el;
  }

  function setMatrixValues(el, values, {round=false}={}){
    const rows = parseInt(el.dataset.rows), cols = parseInt(el.dataset.cols);
    for (let r=0;r<rows;r++){
      for (let c=0;c<cols;c++){
        const idx = r*cols + c;
        const val = values[r]?.[c] ?? values[idx] ?? 0;
        const cell = el.children[idx];
        cell.textContent = round?Math.round(val):fmt(val);
      }
    }
    updateColors(el, values);
  }

  function getMatrixValues(el){
    const rows = parseInt(el.dataset.rows), cols = parseInt(el.dataset.cols);
    const out = makeMatrix(rows, cols);
    for (let r=0;r<rows;r++){
      for (let c=0;c<cols;c++){
        const idx = r*cols + c;
        const cell = el.children[idx];
        const v = parseFloat(cell.textContent || '0');
        out[r][c] = isFinite(v) ? v : 0;
      }
    }
    return out;
  }

  function highlightWindow(matrixEl, r0, c0, k){
    const rows = parseInt(matrixEl.dataset.rows), cols = parseInt(matrixEl.dataset.cols);
    for (let i=0;i<matrixEl.children.length;i++) matrixEl.children[i].classList.remove('highlight');
    for (let dr=0; dr<k; dr++){
      for (let dc=0; dc<k; dc++){
        const r = r0 + dr, c = c0 + dc;
        if (r>=0 && r<rows && c>=0 && c<cols) {
          const idx = r*cols + c;
          matrixEl.children[idx].classList.add('highlight');
        }
      }
    }
  }

  function buildKernelEditor(k){
    kernelEditor.innerHTML = '';
    kernelEditor.style.gridTemplateColumns = `repeat(${k}, 1fr)`;
    for (let i=0;i<k*k;i++){
      const inp = document.createElement('input');
      inp.type = 'number'; inp.step = '0.1'; inp.value = '0';
      kernelEditor.appendChild(inp);
    }
  }

  function getKernelFromEditor(){
    const k = parseInt(kernelSize.value);
    const vals = [];
    const inputs = kernelEditor.querySelectorAll('input');
    for (let r=0;r<k;r++){
      vals[r] = [];
      for (let c=0;c<k;c++){
        const idx = r*k + c; const v = parseFloat(inputs[idx].value||'0');
        vals[r][c] = isFinite(v)? v : 0;
      }
    }
    return vals;
  }

  function setKernelToEditor(mat){
    const k = parseInt(kernelSize.value);
    const inputs = kernelEditor.querySelectorAll('input');
    for (let r=0;r<k;r++){
      for (let c=0;c<k;c++){
        const idx = r*k + c; inputs[idx].value = mat[r]?.[c] ?? 0;
      }
    }
  }

  function normalizeIfBlur(preset){
    if (!['box3','gauss3'].includes(preset)) return;
    const k = getKernelFromEditor();
    let s = 0; k.forEach(row=>row.forEach(v=> s+=v));
    if (Math.abs(s) < 1e-9) return;
    for (let r=0;r<k.length;r++) for (let c=0;c<k.length;c++) k[r][c] = k[r][c]/s;
    setKernelToEditor(k);
  }

  function presetsMatrix(name){
    switch(name){
      case 'identity': return [[0,0,0],[0,1,0],[0,0,0]];
      case 'box3': return [[1,1,1],[1,1,1],[1,1,1]];
      case 'gauss3': return [[1,2,1],[2,4,2],[1,2,1]];
      case 'sharpen': return [[0,-1,0],[-1,5,-1],[0,-1,0]];
      case 'edge_h': return [[1,2,1],[0,0,0],[-1,-2,-1]];
      case 'edge_v': return [[1,0,-1],[2,0,-2],[1,0,-1]];
      case 'laplace': return [[0,1,0],[1,-4,1],[0,1,0]];
      case 'emboss': return [[-2,-1,0],[-1,1,1],[0,1,2]];
      default: return null;
    }
  }

  function computeOutputDims(N, K, stride, padding){
    if (padding==='same') return {rows: Math.ceil(N/stride), cols: Math.ceil(N/stride)};
    const out = Math.floor((N - K)/stride) + 1; return {rows: out, cols: out};
  }

  function padInput(mat, pad){
    const N = mat.length, M = mat[0].length;
    const P = pad; const out = makeMatrix(N+2*P, M+2*P, 0);
    for (let r=0;r<N;r++) for (let c=0;c<M;c++) out[r+P][c+P] = mat[r][c];
    return out;
  }

  function convStep(padded, kernel, r0, c0){
    let sum = 0, prod = makeMatrix(kernel.length, kernel.length);
    for (let r=0; r<kernel.length; r++){
      for (let c=0; c<kernel.length; c++){
        const v = padded[r0+r][c0+c];
        const k = kernel[r][c];
        prod[r][c] = v * k; sum += prod[r][c];
      }
    }
    return {sum, prod};
  }

  const relu = x => Math.max(0, x);

  // ---------- State ----------
  const inpSize = document.getElementById('inpSize');
  const kernelSize = document.getElementById('kernelSize');
  const preset = document.getElementById('preset');
  const presetDesc = document.getElementById('presetDesc');
  const strideSel = document.getElementById('stride');
  const paddingSel = document.getElementById('padding');
  const biasInp = document.getElementById('bias');
  const actSel = document.getElementById('activation');
  const playBtn = document.getElementById('playBtn');
  const stepBtn = document.getElementById('stepBtn');
  const pauseBtn = document.getElementById('pauseBtn');
  const resetBtn = document.getElementById('resetBtn');
  const speed = document.getElementById('speed');
  const randomBtn = document.getElementById('randomInput');
  const gradientBtn = document.getElementById('gradientInput');
  const clearBtn = document.getElementById('clearInput');
  const kernelEditor = document.getElementById('kernelEditor');
  const inputPatternSel = document.getElementById('inputPattern');
  const imgUpload = document.getElementById('imgUpload');

  const inSizeLabel = document.getElementById('inSizeLabel');
  const outSizeLabel = document.getElementById('outSizeLabel');
  const winGrid = document.getElementById('winGrid');
  const kerGrid = document.getElementById('kerGrid');
  const prodGrid = document.getElementById('prodGrid');
  const winLabel = document.getElementById('winLabel');
  const kerLabel = document.getElementById('kerLabel');
  const prodLabel = document.getElementById('prodLabel');
  const sumVal = document.getElementById('sumVal');
  const biasVal = document.getElementById('biasVal');
  const actVal = document.getElementById('actVal');
  const outVal = document.getElementById('outVal');
  const posBadge = document.getElementById('posBadge');
  const effectDesc = document.getElementById('effectDesc');

  let inputMat = []; let outputMat = [];
  let padded = []; let kernel = [];
  let pad = 0; let stride = 1; let K = 3; let N = 12; let outRows=0, outCols=0;

  // animation
  let playing = false; let timer = null; let stepIndex = 0; let coords = [];

  function buildInput(n){
    const curr = document.getElementById('inputGrid');
    const el = createMatrixEl(n, n, {editable:true, kind:'input'});
    if (curr && curr.replaceWith) curr.replaceWith(el);
    el.id = 'inputGrid';
    window.inputGrid = el;
    inSizeLabel.textContent = `${n}×${n}`;

    if (!Array.isArray(inputMat) || inputMat.length !== n) {
      inputMat = Array.from({length:n}, ()=> Array(n).fill(0));
    }
    setMatrixValues(window.inputGrid, inputMat, {round:true});

    el.addEventListener('click', e=>{
      const cell = e.target.closest('.cell'); if (!cell) return;
      const r = parseInt(cell.dataset.r), c = parseInt(cell.dataset.c);
      const delta = e.shiftKey ? -1 : 1;
      const cur = inputMat[r][c] || 0;
      inputMat[r][c] = clamp(cur + delta, 0, 255);
      setMatrixValues(window.inputGrid, inputMat, {round:true});
      preparePadded();
      reset();
    });
  }

  function setInputValues(values){
    setMatrixValues(window.inputGrid, values, {round:true});
    inputMat = getMatrixValues(window.inputGrid);
  }

  function buildOutput(rows, cols){
    const curr = document.getElementById('outputGrid');
    const el = createMatrixEl(rows, cols, {editable:false, kind:'output'});
    if (curr && curr.replaceWith) curr.replaceWith(el);
    el.id='outputGrid';
    window.outputGrid = el;
    outSizeLabel.textContent = `${rows}×${cols}`;
  }

  function refreshKernelEditor(){
    const k = parseInt(kernelSize.value);
    buildKernelEditor(k);
    const p = preset.value;
    const mat = presetsMatrix(p) || makeMatrix(k,k,0);
    setKernelToEditor(mat);
    normalizeIfBlur(p);
    K = k; kernel = getKernelFromEditor();
    presetDesc.textContent = filterDescriptions[p] || '';
    kerLabel.textContent = `${K}×${K}`; prodLabel.textContent = `${K}×${K}`; winLabel.textContent = `${K}×${K}`;
    kerGrid.innerHTML = '';
    const kerEl = createMatrixEl(K,K,{editable:false, kind:'kernel'});
    kerGrid.appendChild(kerEl);
    setMatrixValues(kerEl, kernel);
  }

  function recalcDims(){
    N = parseInt(inpSize.value);
    stride = parseInt(strideSel.value);
    const paddingMode = paddingSel.value;
    pad = paddingMode==='same' ? Math.floor(K/2) : 0;
    const dims = computeOutputDims(N, K, stride, paddingMode);
    outRows = dims.rows; outCols = dims.cols;
    buildOutput(outRows, outCols);
    outputMat = makeMatrix(outRows, outCols, 0);
    setMatrixValues(window.outputGrid, outputMat);
  }

  function preparePadded(){
    inputMat = getMatrixValues(window.inputGrid);
    padded = pad>0 ? padInput(inputMat, pad) : inputMat.map(r=>r.slice());
  }

  function buildCoords(){
    coords = [];
    for (let r=0;r<=padded.length - K; r+=stride){
      for (let c=0;c<=padded[0].length - K; c+=stride){
        coords.push([r,c]);
      }
    }
  }

  function updateStep(idx){
    if (idx<0 || idx>=coords.length) return;
    const [r, c] = coords[idx];
    const oi = Math.floor(r/stride);
    const oj = Math.floor(c/stride);

    highlightWindow(window.inputGrid, r - pad, c - pad, K);

    const {sum, prod} = convStep(padded, kernel, r, c);
    const bias = parseFloat(biasInp.value||'0');
    const raw = sum + bias;
    const activated = (actSel.value==='relu') ? relu(raw) : raw;

    const win = makeMatrix(K,K);
    for (let i=0;i<K;i++) for (let j=0;j<K;j++) win[i][j] = padded[r+i][c+j];

    winGrid.innerHTML = ''; prodGrid.innerHTML = '';
    const wg = createMatrixEl(K,K,{editable:false, kind:'window'});
    const pg = createMatrixEl(K,K,{editable:false, kind:'product'});
    winGrid.appendChild(wg); prodGrid.appendChild(pg);
    setMatrixValues(wg, win, {round:false});
    if (kerGrid.firstElementChild) setMatrixValues(kerGrid.firstElementChild, kernel, {round:false});
    setMatrixValues(pg, prod, {round:false});

    sumVal.textContent = fmt(sum);
    biasVal.textContent = fmt(bias);
    actVal.textContent = actSel.value.toUpperCase();
    outVal.textContent = fmt(activated);
    posBadge.textContent = `(i, j) = (${oi}, ${oj})`;

    outputMat[oi][oj] = activated;
    setMatrixValues(window.outputGrid, outputMat);
  }

  function scheduleNextTick(delay){
    timer = setTimeout(()=>{
      updateStep(stepIndex);
      stepIndex++;
      if (stepIndex >= coords.length) { pause(); return; }
      scheduleNextTick(parseInt(speed.value));
    }, delay);
  }
  function play(){ if (playing) return; playing = true; playBtn.textContent = '⏸ Playing…'; scheduleNextTick(10); }
  function pause(){ playing=false; playBtn.textContent='▶ Play'; if (timer) clearTimeout(timer); }
  function step(){ if (playing) return; updateStep(stepIndex); stepIndex = Math.min(stepIndex+1, coords.length); }
  function reset(){ pause(); stepIndex=0; outputMat = makeMatrix(outRows, outCols, 0); setMatrixValues(window.outputGrid, outputMat); posBadge.textContent='(i, j) = (–, –)'; sumVal.textContent=biasVal.textContent=actVal.textContent=outVal.textContent='–'; highlightWindow(window.inputGrid, -999, -999, K); }

  function randomizeInput(){
    const vals = makeMatrix(N,N);
    for (let r=0;r<N;r++) for (let c=0;c<N;c++) vals[r][c] = Math.round(Math.random()*255);
    setInputValues(vals); preparePadded(); buildCoords(); reset();
  }
  function gradientInputFn(){
    const vals = makeMatrix(N,N);
    for (let r=0;r<N;r++) for (let c=0;c<N;c++) vals[r][c] = Math.round(255 * (r + c) / (2*(N-1)));
    setInputValues(vals); preparePadded(); buildCoords(); reset();
  }
  function clearInputFn(){
    const vals = makeMatrix(N,N,0); setInputValues(vals); preparePadded(); buildCoords(); reset();
  }

  // ---- Pattern generators ----
  function patternChecker(n, period=2){ const vals = makeMatrix(n,n,0); for(let r=0;r<n;r++) for(let c=0;c<n;c++) vals[r][c] = ((Math.floor(r/period)+Math.floor(c/period))%2)*255; return vals; }
  function patternVStripes(n, width=2){ const vals = makeMatrix(n,n,0); for(let r=0;r<n;r++) for(let c=0;c<n;c++) vals[r][c] = (Math.floor(c/width)%2)*255; return vals; }
  function patternHStripes(n, width=2){ const vals = makeMatrix(n,n,0); for(let r=0;r<n;r++) for(let c=0;c<n;c++) vals[r][c] = (Math.floor(r/width)%2)*255; return vals; }
  function patternDiagonal(n){ const vals = makeMatrix(n,n,0); for(let i=0;i<n;i++) vals[i][i] = 255; return vals; }
  function patternCenterDot(n){ const vals = makeMatrix(n,n,0); const r=Math.floor(n/2), c=Math.floor(n/2); vals[r][c]=255; return vals; }
  function patternSquare(n, size=Math.max(2, Math.floor(n/3))){ const vals = makeMatrix(n,n,0); const r0=Math.floor((n-size)/2), c0=r0; for(let r=r0;r<r0+size;r++) for(let c=c0;c<c0+size;c++) vals[r][c]=255; return vals; }
  function patternCircle(n){ const vals = makeMatrix(n,n,0); const cx=(n-1)/2, cy=(n-1)/2; const R=n/4; for(let r=0;r<n;r++) for(let c=0;c<n;c++){ const d=Math.hypot(r-cy,c-cx); vals[r][c]= d<=R?255:0; } return vals; }
  function applyPattern(key){
    if(key==='none') return;
    if(key==='gradient') { gradientInputFn(); return; }
    if(key==='random') { randomizeInput(); return; }
    if(key==='zeros') { clearInputFn(); return; }
    let vals;
    switch(key){
      case 'checker': vals = patternChecker(N); break;
      case 'vstripes': vals = patternVStripes(N); break;
      case 'hstripes': vals = patternHStripes(N); break;
      case 'diagonal': vals = patternDiagonal(N); break;
      case 'center_dot': vals = patternCenterDot(N); break;
      case 'square': vals = patternSquare(N); break;
      case 'circle': vals = patternCircle(N); break;
      default: vals = makeMatrix(N,N,0);
    }
    setInputValues(vals); preparePadded(); buildCoords(); reset();
  }

  // ---- Image loader ----
  function fileToImage(file){
    return new Promise((resolve,reject)=>{
      const fr = new FileReader();
      fr.onload = ()=>{ const img = new Image(); img.onload = ()=> resolve(img); img.onerror=reject; img.src = fr.result; };
      fr.onerror = reject; fr.readAsDataURL(file);
    });
  }
  async function loadImageToInput(file){
    try{
      const img = await fileToImage(file);
      const cvs = document.createElement('canvas'); cvs.width = N; cvs.height = N;
      const ctx = cvs.getContext('2d');
      const scale = Math.max(N/img.width, N/img.height);
      const sw = img.width*scale, sh = img.height*scale;
      const dx = (N - sw)/2, dy = (N - sh)/2;
      ctx.drawImage(img, dx, dy, sw, sh);
      const data = ctx.getImageData(0,0,N,N).data;
      const vals = makeMatrix(N,N,0);
      for(let r=0;r<N;r++) for(let c=0;c<N;c++){
        const i = (r*N + c)*4; const R=data[i], G=data[i+1], B=data[i+2];
        const gray = Math.round(0.299*R + 0.587*G + 0.114*B);
        vals[r][c] = gray;
      }
      setInputValues(vals); preparePadded(); buildCoords(); reset();
    }catch(e){ console.error('Image load failed', e); }
  }

  function updateEffectDesc(){
    const key = preset.value; const base = filterDescriptions[key] || '';
    const padText = (pad>0?`Zero padding ${pad} keeps spatial size.`:'Valid convolution reduces size.');
    const sText = (stride>1?`Stride ${stride} downsamples (skips positions).`:'Stride 1 samples every position.');
    effectDesc.textContent = base + ' ' + padText + ' ' + sText;
  }

  // ---------- Self-tests (console) ----------
  function approxEqual(a,b,eps=1e-6){ return Math.abs(a-b) <= eps; }
  function convSame2D(inp, ker){
    const n = inp.length, k = ker.length, p = Math.floor(k/2);
    const out = makeMatrix(n,n,0);
    const padded = padInput(inp, p);
    for(let r=0;r<n;r++){
      for(let c=0;c<n;c++){
        let s=0;
        for(let i=0;i<k;i++) for(let j=0;j<k;j++) s += padded[r+i][c+j]*ker[i][j];
        out[r][c]=s;
      }
    }
    return out;
  }
  function matrixEqual(A,B){ if(A.length!==B.length||A[0].length!==B[0].length) return false; for(let i=0;i<A.length;i++) for(let j=0;j<A[0].length;j++) if(!approxEqual(A[i][j],B[i][j])) return false; return true; }
  function runSelfTests(){
    console.group('%cConvolution Visualizer — Self Tests','color:#22d3ee');
    // Test 1: Identity should reproduce input (same padding)
    const A = [[1,2,3],[4,5,6],[7,8,9]];
    const I = [[0,0,0],[0,1,0],[0,0,0]];
    const O1 = convSame2D(A,I);
    console.log('Identity same==input:', matrixEqual(A,O1)?'PASS':'FAIL', O1);

    // Test 2: Box blur normalized sum equals local average at center for simple matrix
    const B = [[1,1,1],[1,1,1],[1,1,1]]; // normalized later
    const Bn = B.map(r=>r.slice()); let s=0; Bn.forEach(r=>r.forEach(v=>s+=v));
    for(let r=0;r<3;r++) for(let c=0;c<3;c++) Bn[r][c]/=s;
    const O2 = convSame2D(A,Bn);
    console.log('Box blur produces averages (center ~5):', approxEqual(O2[1][1],5)?'PASS':'WARN', O2);

    // Test 3: Sobel vertical on horizontal gradient > 0 in middle column
    const G = [[0,1,2],[0,1,2],[0,1,2]];
    const SV = [[1,0,-1],[2,0,-2],[1,0,-1]];
    const O3 = convSame2D(G,SV);
    console.log('Sobel V on horiz gradient (edges detected):', (O3[1][0]<0 && O3[1][2]>0)?'PASS':'WARN', O3);

    // Test 4: computeOutputDims valid stride 2 gives 2x2 for N=5,K=3
    const dims = computeOutputDims(5,3,2,'valid');
    console.log('Dims valid stride2 (5,3,2)->2x2:', (dims.rows===2&&dims.cols===2)?'PASS':'FAIL', dims);

    // Test 5: ReLU correctness
    console.log('ReLU(-3)==0 & ReLU(2.5)==2.5:', (relu(-3)===0 && approxEqual(relu(2.5),2.5))?'PASS':'FAIL');

    // Test 6: Laplacian on constant matrix has 0 at center (same padding)
    const C = Array.from({length:5},()=>Array(5).fill(1));
    const L = [[0,1,0],[1,-4,1],[0,1,0]];
    const O4 = convSame2D(C,L);
    console.log('Laplacian center ~0 on constant:', approxEqual(O4[2][2],0)?'PASS':'WARN', O4);

    console.groupEnd();
  }

  // ---------- Event wiring ----------
  document.addEventListener('keydown', e=>{
    if (e.code==='Space'){ e.preventDefault(); playing?pause():play(); }
    if (e.key==='ArrowRight'){ e.preventDefault(); step(); }
  });

  inpSize.addEventListener('change', ()=>{
    N = clamp(parseInt(inpSize.value)||12,4,32); inpSize.value=N;
    buildInput(N);
    if (inputPatternSel.value && inputPatternSel.value !== 'none') { applyPattern(inputPatternSel.value); } else { clearInputFn(); }
    recalcDims(); preparePadded(); buildCoords(); reset();
  });
  kernelSize.addEventListener('change', ()=>{ refreshKernelEditor(); recalcDims(); preparePadded(); buildCoords(); reset(); updateEffectDesc(); });
  preset.addEventListener('change', ()=>{ refreshKernelEditor(); normalizeIfBlur(preset.value); kernel = getKernelFromEditor(); if (kerGrid.firstElementChild) setMatrixValues(kerGrid.firstElementChild, kernel); preparePadded(); buildCoords(); reset(); updateEffectDesc(); });
  strideSel.addEventListener('change', ()=>{ recalcDims(); preparePadded(); buildCoords(); reset(); updateEffectDesc(); });
  paddingSel.addEventListener('change', ()=>{ recalcDims(); preparePadded(); buildCoords(); reset(); updateEffectDesc(); });
  biasInp.addEventListener('change', ()=>{ /* no dim change */ });
  actSel.addEventListener('change', ()=>{ /* no dim change */ });

  inputPatternSel.addEventListener('change', ()=>{ applyPattern(inputPatternSel.value); });
  imgUpload.addEventListener('change', (e)=>{ const f=e.target.files?.[0]; if(f) loadImageToInput(f); });

  // Speed slider live effect
  function handleSpeedChange(){ if (playing){ clearTimeout(timer); scheduleNextTick(parseInt(speed.value)); } }
  speed.addEventListener('input', handleSpeedChange);
  speed.addEventListener('change', handleSpeedChange);

  kernelEditor.addEventListener('input', ()=>{ kernel = getKernelFromEditor(); if (['box3','gauss3'].includes(preset.value)) normalizeIfBlur(preset.value); kernel = getKernelFromEditor(); if (kerGrid.firstElementChild) setMatrixValues(kerGrid.firstElementChild, kernel); reset(); });

  playBtn.addEventListener('click', play);
  pauseBtn.addEventListener('click', pause);
  stepBtn.addEventListener('click', step);
  resetBtn.addEventListener('click', reset);
  randomBtn.addEventListener('click', randomizeInput);
  gradientBtn.addEventListener('click', gradientInputFn);
  clearBtn.addEventListener('click', clearInputFn);

  // ---------- Init ----------
  buildInput(N);
  buildKernelEditor(3);
  preset.value = 'edge_v'; refreshKernelEditor(); normalizeIfBlur('edge_v');
  strideSel.value = '1'; paddingSel.value='same';
  inputPatternSel.value = 'gradient'; gradientInputFn();
  recalcDims(); preparePadded(); buildCoords(); reset(); updateEffectDesc();

  // run console tests
  runSelfTests();
})();
</script>
</body>
</html>
