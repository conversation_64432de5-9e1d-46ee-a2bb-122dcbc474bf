<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Logistic Regression Training Visualizer — Integrated</title>
  <style>
    :root { --bg:#0f172a; --panel:#0b1220; --text:#e5e7eb; --muted:#9ca3af; --accent:#22c55e; --accent2:#60a5fa; --danger:#f87171; --warning:#fbbf24; }
    *{box-sizing:border-box}
    body{margin:0;font-family:ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto,Ubuntu;color:var(--text);background:radial-gradient(1000px 600px at 10% -10%,#1f2937,var(--bg))}
    .wrap{max-width:1200px;margin:24px auto;padding:16px}
    .grid{display:grid;grid-template-columns:1.2fr .8fr;gap:16px}
    .card{background:linear-gradient(180deg,rgba(255,255,255,.04),rgba(255,255,255,.02));border:1px solid rgba(255,255,255,.07);border-radius:16px;box-shadow:0 10px 30px rgba(0,0,0,.25)}
    .card h2{margin:0;padding:14px 16px;border-bottom:1px solid rgba(255,255,255,.06);font-size:18px;font-weight:600;color:#f8fafc}
    .card .body{padding:16px}

    .canvases{display:grid;gap:12px}
    canvas{width:100%;height:360px;background:#0a1120;border:1px solid rgba(255,255,255,.06);border-radius:16px}
    #lossCanvas{height:180px}

    .legend{display:flex;gap:16px;font-size:13px;color:var(--muted);margin-top:8px}
    .dot{display:inline-block;width:10px;height:10px;border-radius:50%;margin-right:6px}
    .dot.data0{background:#60a5fa}
    .dot.data1{background:#f472b6}
    .dot.sig{background:var(--accent)}
    .dot.prev{background:#a78bfa}

    .equation{margin-top:10px;font-family:ui-monospace,Consolas,Menlo,monospace;color:#c7d2fe;background:var(--panel);border:1px solid rgba(255,255,255,.08);padding:10px 12px;border-radius:10px}

    .controls{display:grid;grid-template-columns:1fr 1fr;gap:12px}
    .row{display:grid;grid-template-columns:130px 1fr 90px;gap:10px;align-items:center}
    label{color:var(--muted);font-size:12px}
    input[type=number],input[type=range]{width:100%;background:#0b1220;color:var(--text);border:1px solid rgba(255,255,255,.08);border-radius:10px;padding:8px 10px}
    input[type=range]{appearance:none;height:6px;border-radius:999px;background:#222a3b}
    input[type=range]::-webkit-slider-thumb{appearance:none;width:16px;height:16px;border-radius:999px;background:var(--accent2);border:2px solid white;box-shadow:0 1px 4px rgba(0,0,0,.4)}
    .btns{display:flex;gap:10px;flex-wrap:wrap;margin-top:8px}
    button{border:1px solid rgba(255,255,255,.12);color:#e5e7eb;background:#101626;padding:10px 14px;border-radius:12px;cursor:pointer;font-weight:600;letter-spacing:.2px;transition:transform .06s ease,background .2s ease,border-color .2s ease}
    button:hover{transform:translateY(-1px);border-color:rgba(255,255,255,.22)}
    .primary{background:linear-gradient(135deg,#1d4ed8,#22c55e);border:none}
    .danger{background:linear-gradient(135deg,#991b1b,#ef4444);border:none}
    .warn{background:linear-gradient(135deg,#92400e,#f59e0b);border:none}
    .muted{background:#0b1220}

    .status{display:grid;grid-template-columns:repeat(2,1fr);gap:10px;margin-top:12px}
    .stat{background:#0b1220;border:1px solid rgba(255,255,255,.08);border-radius:12px;padding:12px}
    .stat .k{color:var(--muted);font-size:12px}
    .stat .v{font-size:18px;font-weight:700}

    .mode{display:flex;gap:8px;align-items:center;margin:8px 0 10px}
    .badge{display:inline-block;padding:4px 8px;border-radius:999px;font-size:12px;border:1px solid rgba(255,255,255,.15)}
    .phase-0{background:#0b3b1d;border-color:#1f7a3e}
    .phase-1{background:#38210b;border-color:#8a5a10}
    .phase-2{background:#2a223f;border-color:#6b5ca3}
    .phase-3{background:#401d2b;border-color:#b0476c}

    .calc-grid{display:grid;grid-template-columns:1fr;gap:10px}
    .calc-box{background:#0b1220;border:1px solid rgba(255,255,255,.1);border-radius:12px;padding:10px}
    .calc-box h3{margin:0 0 6px 0;font-size:14px;color:#dbeafe}
    .mono{font-family:ui-monospace,Consolas,Menlo,monospace;white-space:pre-wrap}
    .scroll{max-height:220px;overflow:auto}

    @media (max-width:980px){.grid{grid-template-columns:1fr}}
  </style>
</head>
<body>
  <div class="wrap">
    <div class="grid">
      <!-- Viz -->
      <div class="card">
        <h2>Logistic Regression — Training Playground <span id="phaseBadge" class="badge phase-0" style="margin-left:8px">Classic mode</span></h2>
        <div class="body">
          <div class="canvases">
            <canvas id="plotCanvas"></canvas>
            <canvas id="lossCanvas"></canvas>
          </div>
          <div class="legend">
            <span><span class="dot data0"></span> Class 0</span>
            <span><span class="dot data1"></span> Class 1</span>
            <span><span class="dot sig"></span> p(x)=σ(w·x+b)</span>
            <span><span class="dot prev"></span> Previous p(x) (after update)</span>
          </div>
          <div class="equation" id="equation">p(x) = σ(w·x + b)</div>
        </div>
      </div>

      <!-- Controls -->
      <div class="card">
        <h2>Controls</h2>
        <div class="body">
          <div class="mode">
            <label style="color:var(--muted);font-size:12px">Mode:</label>
            <button id="modeClassic" class="muted">Classic</button>
            <button id="modePhased" class="muted">Multi‑phase</button>
          </div>
          <div class="controls">
            <div class="row"><label># Points</label><input id="points" type="range" min="10" max="500" value="160" step="10"/><input id="pointsNum" type="number" min="10" max="500" value="160"/></div>
            <div class="row"><label>Noise σ</label><input id="noise" type="range" min="0" max="2" value="0.6" step="0.01"/><input id="noiseNum" type="number" min="0" max="2" value="0.6" step="0.01"/></div>
            <div class="row"><label>True weight w*</label><input id="trueW" type="range" min="-4" max="4" value="2.2" step="0.1"/><input id="trueWNum" type="number" min="-4" max="4" value="2.2" step="0.1"/></div>
            <div class="row"><label>True bias b*</label><input id="trueB" type="range" min="-6" max="6" value="-0.3" step="0.1"/><input id="trueBNum" type="number" min="-6" max="6" value="-0.3" step="0.1"/></div>
            <div class="row"><label>Learning rate η</label><input id="lr" type="range" min="0.0005" max="0.5" value="0.05" step="0.0005"/><input id="lrNum" type="number" min="0.0005" max="0.5" value="0.05" step="0.0005"/></div>
            <div class="row"><label>Epochs</label><input id="epochs" type="range" min="1" max="2000" value="300" step="1"/><input id="epochsNum" type="number" min="1" max="2000" value="300" step="1"/></div>
            <div class="row"><label>Batch size</label><input id="batch" type="range" min="1" max="500" value="160" step="1"/><input id="batchNum" type="number" min="1" max="500" value="160" step="1"/></div>
          </div>
          <div class="btns">
            <button class="primary" id="btnStart">Start</button>
            <button class="muted" id="btnStep">Step</button>
            <button class="muted" id="btnNextPhase" style="display:none">Next Phase</button>
            <button class="warn" id="btnShuffle">Shuffle Data</button>
            <button class="danger" id="btnReset">Reset</button>
            <button class="muted" id="btnRegenerate">Regenerate Dataset</button>
          </div>
          <div class="status">
            <div class="stat"><div class="k">Epoch</div><div class="v" id="statEpoch">0</div></div>
            <div class="stat"><div class="k">Loss (BCE)</div><div class="v" id="statLoss">—</div></div>
            <div class="stat"><div class="k">w</div><div class="v" id="statW">0.00</div></div>
            <div class="stat"><div class="k">b</div><div class="v" id="statB">0.00</div></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Calculations (Multi‑phase) -->
    <div id="calcCard" class="card" style="margin-top:16px; display:none">
      <h2>Live Calculations</h2>
      <div class="body calc-grid">
        <div class="calc-box"><h3 id="phaseTitle">Phase 1 — Evaluate p̂</h3><div class="mono" id="calcMain">—</div></div>
        <div class="calc-box"><h3>Mini‑batch table (first 50)</h3><div class="mono scroll" id="tableBox">—</div></div>
      </div>
    </div>
  </div>

  <script>
    // ===== Utilities =====
    const el = (id)=>document.getElementById(id);
    const RNG=(seed=1234567)=>{let s=seed>>>0;return()=>{s=(s*1664525+1013904223)>>>0;return s/0xffffffff;}};
    const bindRangePair=(rangeEl,numEl,onChange)=>{const sync=(src,dst)=>{dst.value=src.value;onChange(parseFloat(src.value));};rangeEl.addEventListener('input',()=>sync(rangeEl,numEl));numEl.addEventListener('input',()=>sync(numEl,rangeEl));onChange(parseFloat(rangeEl.value));};

    // ===== Global State =====
    const state={
      seed:42,
      data:{x:[],y:[]}, // y in {0,1}
      true:{w:2.2,b:-0.3,sigma:0.6},
      model:{w:0,b:0},
      train:{lr:0.05,epochs:300,batch:160,epoch:0,running:false},
      history:[],
      scale:{xmin:-1,xmax:1,ymin:-0.1,ymax:1.1},
      mode:'classic', // 'classic'|'phased'
      phase:0,
      prevLine:null,
      batchCache:null
    };

    // ===== Data Generation =====
    function gaussian(rand){let u=0,v=0;while(u===0)u=rand();while(v===0)v=rand();return Math.sqrt(-2*Math.log(u))*Math.cos(2*Math.PI*v)}
    function sigmoid(z){return 1/(1+Math.exp(-z))}

    function generateData(n=160){
      const rand=RNG(state.seed);
      const x=new Array(n).fill(0).map(()=> (rand()*10-5) + gaussian(rand)*state.true.sigma );
      // Bernoulli labels from true logistic model
      const p=x.map(v=>sigmoid(state.true.w*v + state.true.b));
      const y=p.map(pi=> (rand()<pi?1:0) );
      state.data={x,y};
      fitScale();
      state.history=[]; state.train.epoch=0; state.prevLine=null; state.phase=0; state.batchCache=null;
      updateStats(0,state.model.w,state.model.b); drawAll(); updateEquation();
    }

    function fitScale(){const {x}=state.data;const pad=.2;const xmin=Math.min(...x),xmax=Math.max(...x);const xr=xmax-xmin;state.scale={xmin:xmin-xr*pad,xmax:xmax+xr*pad,ymin:-0.1,ymax:1.1};}

    // ===== Model, Loss, Grads =====
    const predictProb=(x,w=state.model.w,b=state.model.b)=>sigmoid(w*x + b);

    function bce(xs,ys){ // binary cross-entropy
      const eps=1e-9; const n=xs.length; let s=0;
      for(let i=0;i<n;i++){const p=predictProb(xs[i]); const y=ys[i]; s += -( y*Math.log(Math.max(p,eps)) + (1-y)*Math.log(Math.max(1-p,eps)) ); }
      return s/n;
    }

    function grads(xs,ys){ // gradient of BCE wrt w,b using logistic
      const n=xs.length; let gW=0, gB=0; const rows=[]; for(let i=0;i<n;i++){ const x=xs[i]; const y=ys[i]; const p=predictProb(x); const diff=p - y; gW += diff*x; gB += diff; if(i<50){ rows.push({x,y,p,diff,dx:diff*x}); } }
      return { dw: gW/n, db: gB/n, rows };
    }

    // ===== Classic step =====
    function stepClassic(){ const {x,y}=state.data; const n=x.length; const b=Math.max(1,Math.min(state.train.batch,n));
      const idx=[...Array(n).keys()]; for(let i=n-1;i>0;i--){const j=Math.floor(Math.random()*(i+1)); [idx[i],idx[j]]=[idx[j],idx[i]];}
      const xs=idx.slice(0,b).map(i=>x[i]); const ys=idx.slice(0,b).map(i=>y[i]);
      const {dw,db}=grads(xs,ys);
      state.model.w -= state.train.lr*dw; state.model.b -= state.train.lr*db;
      const loss=bce(x,y); state.history.push(loss); state.train.epoch+=1;
      updateStats(loss,state.model.w,state.model.b); drawAll(); if(state.train.epoch>=state.train.epochs) state.train.running=false; }

    // ===== Phased step engine =====
    function prepareBatch(){const {x,y}=state.data; const n=x.length; const b=Math.max(1,Math.min(state.train.batch,n)); const idx=[...Array(n).keys()]; for(let i=n-1;i>0;i--){const j=Math.floor(Math.random()*(i+1)); [idx[i],idx[j]]=[idx[j],idx[i]];} const xs=idx.slice(0,b).map(i=>x[i]); const ys=idx.slice(0,b).map(i=>y[i]); state.batchCache={xs,ys,p:null,diff:null,sumDiff:null,sumDiffX:null,dw:null,db:null,dwVal:null,dbVal:null}; }

    function nextPhase(){
      switch(state.phase){
        case 0: // evaluate p
          if(!state.batchCache) prepareBatch(); const {xs,ys}=state.batchCache; const p=xs.map(v=>predictProb(v)); state.batchCache.p=p; renderPhase(0,{xs,ys,p}); state.phase=1; break;
        case 1: // errors & sums
          const diff=state.batchCache.p.map((pv,i)=> pv - state.batchCache.ys[i]); const sumDiff=diff.reduce((a,c)=>a+c,0); const sumDiffX=diff.reduce((a,c,i)=>a+c*state.batchCache.xs[i],0);
          Object.assign(state.batchCache,{diff,sumDiff,sumDiffX}); renderPhase(1,{diff,sumDiff,sumDiffX}); state.phase=2; break;
        case 2: // grads & deltas
          const n=state.batchCache.xs.length; const dw=(1/n)*state.batchCache.sumDiffX; const db=(1/n)*state.batchCache.sumDiff; const dwVal=state.train.lr*dw; const dbVal=state.train.lr*db; Object.assign(state.batchCache,{dw,db,dwVal,dbVal}); renderPhase(2,{n,dw,db,dwVal,dbVal,eta:state.train.lr}); state.phase=3; break;
        case 3: // apply update
          state.prevLine={w:state.model.w,b:state.model.b}; state.model.w -= state.batchCache.dwVal; state.model.b -= state.batchCache.dbVal; const fullLoss=bce(state.data.x,state.data.y); state.history.push(fullLoss); state.train.epoch+=1; updateStats(fullLoss,state.model.w,state.model.b); drawAll(); renderPhase(3,{loss:fullLoss}); state.phase=0; state.batchCache=null; break;
      }
      updatePhaseUI();
    }

    // ===== Rendering (plots) =====
    const plot=el('plotCanvas'); const loss=el('lossCanvas'); const pctx=plot.getContext('2d'); const lctx=loss.getContext('2d');
    function mapX(x){const {xmin,xmax}=state.scale; return (x-xmin)/(xmax-xmin)*(plot.width-40)+20}
    function mapYprob(y){const {ymin,ymax}=state.scale; return (1-(y-ymin)/(ymax-ymin))*(plot.height-40)+20}

    function drawGrid(ctx,w,h){ctx.save();ctx.strokeStyle='rgba(255,255,255,.06)';ctx.lineWidth=1;for(let x=40;x<w;x+=40){ctx.beginPath();ctx.moveTo(x,0);ctx.lineTo(x,h);ctx.stroke();}for(let y=40;y<h;y+=40){ctx.beginPath();ctx.moveTo(0,y);ctx.lineTo(w,y);ctx.stroke();}ctx.restore();}
    function clearCanvas(c){const ctx=c.getContext('2d'); ctx.clearRect(0,0,c.width,c.height);}
    function resizeCanvases(){const dpr=window.devicePixelRatio||1; [plot,loss].forEach(c=>{const rect=c.getBoundingClientRect(); c.width=Math.floor(rect.width*dpr); c.height=Math.floor(rect.height*dpr); c.getContext('2d').setTransform(dpr,0,0,dpr,0,0);});}

    function drawAll(){ resizeCanvases(); drawScatterAndSigmoid(); drawLoss(); updateEquation(); }

    function drawScatterAndSigmoid(){
      clearCanvas(plot); const w=plot.width/(window.devicePixelRatio||1); const h=plot.height/(window.devicePixelRatio||1); drawGrid(pctx,w,h);
      pctx.strokeStyle='rgba(255,255,255,.25)'; pctx.lineWidth=1.5; pctx.strokeRect(20,20,w-40,h-40);
      // data points (jitter y a bit for visibility)
      for(let i=0;i<state.data.x.length;i++){
        const xi=state.data.x[i]; const yi=state.data.y[i]; const yj=yi + (Math.random()-0.5)*0.04; pctx.beginPath(); pctx.arc(mapX(xi), mapYprob(yj), 3, 0, Math.PI*2); pctx.fillStyle = yi? '#f472b6' : '#60a5fa'; pctx.fill();
      }
      // previous sigmoid (dashed)
      if(state.prevLine){ pctx.strokeStyle='#a78bfa'; pctx.lineWidth=1.5; pctx.setLineDash([6,6]); pctx.beginPath(); for(let t=0;t<=100;t++){ const xv = state.scale.xmin + (t/100)*(state.scale.xmax-state.scale.xmin); const pv = sigmoid(state.prevLine.w*xv + state.prevLine.b); const xp=mapX(xv), yp=mapYprob(pv); if(t===0) pctx.moveTo(xp,yp); else pctx.lineTo(xp,yp);} pctx.stroke(); pctx.setLineDash([]); }
      // current sigmoid
      pctx.strokeStyle='#22c55e'; pctx.lineWidth=2.5; pctx.beginPath(); for(let t=0;t<=100;t++){ const xv = state.scale.xmin + (t/100)*(state.scale.xmax-state.scale.xmin); const pv = sigmoid(state.model.w*xv + state.model.b); const xp=mapX(xv), yp=mapYprob(pv); if(t===0) pctx.moveTo(xp,yp); else pctx.lineTo(xp,yp);} pctx.stroke();
    }

    function drawLoss(){ clearCanvas(loss); const w=loss.width/(window.devicePixelRatio||1); const h=loss.height/(window.devicePixelRatio||1); drawGrid(lctx,w,h); lctx.strokeStyle='rgba(255,255,255,.25)'; lctx.lineWidth=1.5; lctx.strokeRect(20,20,w-40,h-40); if(state.history.length===0) return; const padL=30,padR=20,padT=20,padB=30; const xmin=0,xmax=state.history.length-1; const ymin=Math.min(...state.history),ymax=Math.max(...state.history); const mapLX=(x)=>padL+(x-xmin)/Math.max(1,(xmax-xmin))*(w-padL-padR); const mapLY=(y)=>padT+(1-(y-ymin)/Math.max(1e-9,(ymax-ymin)))*(h-padT-padB); lctx.strokeStyle='#fbbf24'; lctx.lineWidth=2; lctx.beginPath(); lctx.moveTo(mapLX(0),mapLY(state.history[0])); for(let i=1;i<state.history.length;i++){ lctx.lineTo(mapLX(i),mapLY(state.history[i])); } lctx.stroke(); lctx.fillStyle='rgba(229,231,235,.9)'; lctx.font='12px ui-sans-serif,system-ui'; lctx.fillText('Epoch',w/2-18,h-8); lctx.save(); lctx.translate(8,h/2+20); lctx.rotate(-Math.PI/2); lctx.fillText('Loss (BCE)',0,0); lctx.restore(); }

    // ===== Equation & Stats =====
    function updateEquation(){ const w=state.model.w.toFixed(6); const b=state.model.b.toFixed(6); el('equation').textContent=`p(x) = σ(${w}·x + ${b})`;}
    function updateStats(lossVal,w,b){ el('statEpoch').textContent=state.train.epoch.toString(); el('statLoss').textContent=(isFinite(lossVal)?lossVal.toFixed(6):'—'); el('statW').textContent=w.toFixed(6); el('statB').textContent=b.toFixed(6); }

    // ===== Calculations (UI) =====
    function renderTable(rows){ const header='i   x\t\t y\t\t p̂=σ(w·x+b)\t diff=(p̂−y)\t diff·x\n' + '-'.repeat(84)+'\n'; const lines = rows.map((r,i)=>{ const pad=v=> (Number.isFinite(v)?v.toFixed(6):'—').padStart(10,' '); return `${String(i).padStart(3,' ')} ${pad(r.x)} ${pad(r.y)} ${pad(r.p)} ${pad(r.diff)} ${pad(r.dx)}`; }); el('tableBox').textContent = header + lines.join('\n'); }
    function renderPhase(p,info){ const titles=['Phase 1 — Evaluate p̂','Phase 2 — Errors & sums','Phase 3 — Gradients & deltas','Phase 4 — Apply update']; el('phaseTitle').textContent=titles[p]; const fmt=(n)=>Number.isFinite(n)?n.toFixed(6):'—'; if(p===0){ const {xs,ys,p}=info; el('calcMain').textContent=`For each: p̂ᵢ = σ(w·xᵢ + b)\nCurrent w=${fmt(state.model.w)}, b=${fmt(state.model.b)}`; renderTable(xs.slice(0,50).map((x,i)=>({x, y:ys[i], p:p[i], diff:NaN, dx:NaN}))); } if(p===1){ const {diff,sumDiff,sumDiffX}=info; el('calcMain').textContent=`diffᵢ = p̂ᵢ − yᵢ\nΣ diff = ${fmt(sumDiff)}\nΣ (diff·x) = ${fmt(sumDiffX)}`; const {xs,ys,p}=state.batchCache; renderTable(xs.slice(0,50).map((x,i)=>({x, y:ys[i], p:p[i], diff:diff[i], dx:diff[i]*x}))); } if(p===2){ const {n,dw,db,dwVal,dbVal,eta}=info; el('calcMain').textContent=`n=${n}\n∂L/∂w = (1/n) Σ(diff·x) = ${fmt(dw)}\n∂L/∂b = (1/n) Σ diff   = ${fmt(db)}\nη=${fmt(eta)}\nΔw=η·∂L/∂w=${fmt(dwVal)}\nΔb=η·∂L/∂b=${fmt(dbVal)}`; } if(p===3){ el('calcMain').textContent=`Apply: w ← w − Δw, b ← b − Δb\nNew w=${fmt(state.model.w)}, b=${fmt(state.model.b)}\nFull‑dataset BCE = ${fmt(info.loss)}`; } }

    function updatePhaseUI(){ const badge=el('phaseBadge'); if(state.mode==='classic'){ badge.textContent='Classic mode'; badge.className='badge phase-0'; el('btnNextPhase').style.display='none'; el('calcCard').style.display='none'; } else { const titles=['Phase 1 — Evaluate p̂','Phase 2 — Errors & sums','Phase 3 — Gradients & deltas','Phase 4 — Apply update']; badge.textContent=titles[state.phase]; badge.className='badge phase-'+state.phase; el('btnNextPhase').style.display='inline-block'; el('calcCard').style.display='block'; } updateEquation(); drawAll(); }

    // ===== Events =====
    function setMode(mode){ state.mode=mode; state.phase=0; state.batchCache=null; updatePhaseUI(); }
    el('modeClassic').addEventListener('click',()=>setMode('classic'));
    el('modePhased').addEventListener('click',()=>setMode('phased'));

    el('btnRegenerate').addEventListener('click',()=>{ generateData(Math.round(parseFloat(el('points').value))); });
    el('btnShuffle').addEventListener('click',()=>{ state.seed=Math.floor(Math.random()*1e9); generateData(Math.round(parseFloat(el('points').value))); });
    el('btnReset').addEventListener('click',()=>{ state.model={w:0,b:0}; state.history=[]; state.train.epoch=0; state.prevLine=null; state.phase=0; state.batchCache=null; updateStats(0,state.model.w,state.model.b); drawAll(); updatePhaseUI(); el('calcMain').textContent='—'; el('tableBox').textContent='—'; });

    el('btnStart').addEventListener('click',(e)=>{ state.train.running=!state.train.running; e.target.textContent=state.train.running?'Pause':'Start'; if(state.train.running) trainLoop(); });
    el('btnStep').addEventListener('click',()=>{ if(!state.train.running){ if(state.mode==='classic'){ stepClassic(); } else { nextPhase(); } } });
    el('btnNextPhase').addEventListener('click',()=>{ nextPhase(); });

    function trainLoop(){ if(!state.train.running) return; if(state.mode==='classic'){ stepClassic(); } else { nextPhase(); } requestAnimationFrame(trainLoop); }

    // ===== Bind controls =====
    bindRangePair(el('points'), el('pointsNum'), v=>{ state.train.batch=Math.min(state.train.batch,Math.round(v)); el('batch').max=v; el('batchNum').max=v; });
    bindRangePair(el('noise'), el('noiseNum'), v=>{ state.true.sigma=v; });
    bindRangePair(el('trueW'), el('trueWNum'), v=>{ state.true.w=v; });
    bindRangePair(el('trueB'), el('trueBNum'), v=>{ state.true.b=v; });
    bindRangePair(el('lr'), el('lrNum'), v=>{ state.train.lr=v; });
    bindRangePair(el('epochs'), el('epochsNum'), v=>{ state.train.epochs=Math.round(v); });
    bindRangePair(el('batch'), el('batchNum'), v=>{ state.train.batch=Math.max(1,Math.round(v)); });

    // ===== Init =====
    window.addEventListener('resize',()=>{ drawAll(); });
    generateData(160);
    updatePhaseUI();
    updateEquation();
  </script>
</body>
</html>
