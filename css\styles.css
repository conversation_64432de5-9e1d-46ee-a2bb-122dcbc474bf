:root{
  --bg:#0b1226;
  --panel:#0f172a;
  --soft:#15213a;
  --text:#e5e7eb;
  --muted:#94a3b8;
  --accent:#38bdf8;
  --accent-2:#f59e0b;
  --good:#22c55e;
  --bad:#ef4444;
  --ink:#cbd5e1;
}
*{box-sizing:border-box}
html,body{height:100%}
body{
  margin:0;
  font-family:Inter,Segoe UI,Roboto,Arial,sans-serif;
  background:linear-gradient(180deg,var(--bg),#041322);
  color:#d7eefc;
}
.app-shell{display:flex;flex-direction:column;min-height:100vh}
.topbar{display:flex;align-items:center;justify-content:space-between;padding:18px 28px;background:rgba(0,0,0,0.15);backdrop-filter:blur(6px);border-bottom:1px solid rgba(255,255,255,0.03)}
.brand{font-weight:700}
.nav .btn{margin-left:10px;padding:8px 12px;border-radius:8px;background:linear-gradient(90deg,var(--accent),#2bb6e0);color:#02202b;text-decoration:none;font-weight:600}
.content{flex:1;padding:28px}
.hero{max-width:1100px;margin:10px auto}
.cards{display:flex;gap:18px;margin-top:18px}
.card{display:block;padding:18px;background:linear-gradient(180deg,rgba(255,255,255,0.02),rgba(255,255,255,0.01));border-radius:10px;width:320px;text-decoration:none;color:inherit;border:1px solid rgba(255,255,255,0.03)}
.card h3{margin:0 0 8px}
.footer{padding:14px 28px;text-align:center;color:var(--muted);font-size:13px}
/* demo layout */
.demo-shell{display:grid;grid-template-columns:1fr 360px;gap:18px;align-items:start;max-width:1100px;margin:12px auto}
.panel{background:linear-gradient(180deg,var(--panel),#071421);padding:16px;border-radius:12px;border:1px solid rgba(255,255,255,0.03)}
.canvas-area{height:560px;border-radius:10px;background:linear-gradient(180deg,#071828,#021219);display:flex;align-items:center;justify-content:center}
/* SVG & plot polish */
.plot-wrap{position:relative}
svg{width:100%;height:480px;display:block;background:radial-gradient(1200px 600px at 20% 10%, rgba(56,189,248,.10), transparent 60%);border-radius:10px}
.gridline{stroke:#1e293b;opacity:.6}
.label{font-size:12px; fill: var(--ink); paint-order: stroke; stroke: #0b1226; stroke-width: 3px}

/* edges & nodes */
.node{stroke:#93c5fd;stroke-width:2;fill:#0b1a30}
.edge{stroke:#64748b;stroke-width:1.2;opacity:.85}
.edge.pos{stroke:var(--good)}
.edge.neg{stroke:var(--bad)}

/* smooth transitions for SVG elements */
svg .edge, svg .tangent, svg .curve, svg circle{transition:stroke 240ms ease, stroke-width 240ms ease, fill 240ms ease, opacity 240ms ease}
svg text.label, svg text.badge, svg text.grad-label{transition:fill 240ms ease, opacity 240ms ease}
.controls{display:flex;flex-direction:column;gap:12px}
.control{padding:10px;border-radius:8px;background:rgba(255,255,255,0.02)}
.btn-row{display:flex;gap:8px}
.btn-action{padding:8px 12px;border-radius:8px;background:var(--accent);color:#02202b;text-decoration:none;border:none;cursor:pointer}
.small-log{height:240px;overflow:auto;background:#021318;padding:8px;border-radius:8px;font-family:monospace;font-size:13px}
.kv{display:flex;align-items:center;justify-content:space-between}
.label{color:var(--muted);font-size:13px}
input[type=range]{width:100%}

.legend{display:flex;gap:8px;align-items:center;margin-top:8px}
.legend .item{display:flex;gap:6px;align-items:center;font-size:13px;color:var(--muted)}
.weight-label{font-family:monospace;font-size:12px;color:#cfeefb}
.math-block{white-space:pre-wrap;font-family:monospace;font-size:13px;background:#021318;padding:8px;border-radius:8px}
/* responsive */
@media (max-width:900px){.demo-shell{grid-template-columns:1fr}}
