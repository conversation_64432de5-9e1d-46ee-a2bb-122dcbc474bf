<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Neural Network — ML Visual Playground</title>
  <link rel="stylesheet" href="css/styles.css">
</head>
<body>
  <div class="app-shell">
    <header class="topbar">
      <div class="brand">ML Visual Playground</div>
      <nav class="nav">
        <a href="index.html" class="btn">Home</a>
        <a href="gradient-descent.html" class="btn">Gradient Descent</a>
      </nav>
    </header>

    <main class="content">
      <div class="demo-shell">
        <div class="panel">
          <div class="canvas-area" id="nn-canvas"></div>
          <div style="display:flex;gap:8px;margin-top:12px;align-items:center;">
            <button id="step-forward" class="btn-action">Step Forward</button>
            <button id="step-back" class="btn-action">Backprop Step</button>
            <button id="reset-nn" class="btn-action">Reset</button>
          </div>
        </div>

        <aside class="panel controls">
          <div class="control">
            <div class="label">Hidden units: <span id="hidden-val">3</span></div>
            <input type="range" id="hidden" min="1" max="10" step="1" value="3">
          </div>
          <div class="control">
            <div class="label">Activation: <span id="act-val">tanh</span></div>
            <select id="act">
              <option>tanh</option>
              <option>relu</option>
              <option>sigmoid</option>
            </select>
          </div>

          <div class="control">
            <div class="label">Forward pass</div>
            <div class="btn-row">
              <button id="fn-run" class="btn-action">Run</button>
              <button id="fn-rand" class="btn-action">Randomize weights</button>
            </div>
          </div>

          <div class="control">
            <div class="label">Output</div>
            <div class="small-log" id="nn-log"></div>
            <div style="height:12px"></div>
            <div class="label">Calculations</div>
            <div class="math-block" id="nn-math"></div>
            <div class="legend" aria-hidden="true">
              <div class="item"><span style="width:18px;height:10px;background:linear-gradient(90deg,#ff6b6b,#3b82f6);display:inline-block;border-radius:3px"></span><div class="weight-label">weight color: red (-) → blue (+)</div></div>
              <div class="item"><div class="weight-label">line width ∝ |weight|</div></div>
            </div>
          </div>
        </aside>
      </div>
    </main>

    <footer class="footer">Small demo</footer>
  </div>

  <script src="js/neural.js"></script>
</body>
</html>
