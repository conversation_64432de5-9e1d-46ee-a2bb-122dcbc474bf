<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>ML Visual Playground — GD + NN (Forward & Backprop)</title>
<style>
  :root{
    --bg:#0b1226; --panel:#0f172a; --soft:#15213a; --text:#e5e7eb; --muted:#94a3b8;
    --accent:#38bdf8; --accent2:#f59e0b; --good:#22c55e; --bad:#ef4444; --ink:#cbd5e1;
  }
  *{box-sizing:border-box}
  body{margin:0;background:linear-gradient(180deg,var(--bg),#0a0f21 70%);color:var(--text);
       font-family:ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto,"Helvetica Neue",Arial}
  .app{max-width:1200px;margin:24px auto;padding:16px}
  h1{margin:0 0 6px 0;font-size:clamp(1.2rem,2.2vw,1.8rem)}
  .sub{color:var(--muted);margin-bottom:14px}

  /* Tabs */
  .tabs{display:flex;gap:8px;flex-wrap:wrap;margin-bottom:12px}
  .tab{padding:10px 14px;border-radius:12px;border:1px solid #21304d;cursor:pointer;
       background:linear-gradient(180deg,#0f1a33,#0c1427);color:var(--text);font-weight:700}
  .tab[aria-selected="true"]{outline:2px solid var(--accent); color:#06131f; background:linear-gradient(180deg,var(--accent),#179bd6)}

  .panel{background:linear-gradient(180deg,var(--panel),#0c1427); border:1px solid #20304f;
         border-radius:16px; padding:14px; box-shadow:0 12px 30px rgba(0,0,0,.28)}
  .grid{display:grid;grid-template-columns:1.2fr .8fr;gap:16px}
  @media (max-width: 980px){.grid{grid-template-columns:1fr}}
  .row{display:flex;gap:8px;align-items:center;flex-wrap:wrap}
  .control{background:var(--soft);border:1px solid #21304d;border-radius:12px;padding:10px 12px}
  .control label{display:block;font-size:.9rem;color:var(--muted);margin-bottom:6px}
  .control input[type=range]{width:100%}
  .pill{display:inline-flex;align-items:center;gap:8px;padding:6px 10px;border-radius:999px;border:1px solid #21304d;background:#0c172b}
  button{border:0;border-radius:12px;padding:10px 12px;cursor:pointer;color:#071421;font-weight:800;
         background:linear-gradient(180deg,var(--accent),#179bd6)}
  button.ghost{background:transparent;border:1px solid #2a3a57;color:var(--text)}

  .math{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace}
  .math .hl{color:var(--accent)}.math .hl2{color:var(--accent2)}.ok{color:var(--good)}.warn{color:var(--bad)}
  .panel-title{font-size:.92rem;color:var(--muted);margin:8px 0}

  /* Shared Plot styles */
  .plot-wrap{position:relative}
  svg{width:100%;height:480px;display:block;background:radial-gradient(1200px 600px at 20% 10%, rgba(56,189,248,.10), transparent 60%)}
  .gridline{stroke:#1e293b;opacity:.6}
  .label{font-size:12px; fill: var(--ink); paint-order: stroke; stroke: #0b1226; stroke-width: 3px;}

  /* GD-specific */
  .curve{fill:none;stroke:var(--accent);stroke-width:2.6}
  .tangent{stroke:var(--accent2);stroke-width:1.6;stroke-dasharray:6 6}
  .point{fill:#fff;stroke:var(--accent);stroke-width:2}
  .next-point{fill:#fff;stroke:var(--good);stroke-width:2}
  .arrow{stroke:var(--good);stroke-width:2;marker-end:url(#arrowHead)}
  .ghost-x{stroke:#334155;stroke-dasharray:4 6}

  /* NN-specific */
  .node{stroke:#93c5fd;stroke-width:2;fill:#0b1a30}
  .edge{stroke:#64748b;stroke-width:1.2;opacity:.7}
  .edge.pos{stroke:var(--good)}
  .edge.neg{stroke:var(--bad)}
  .badge{font-size:11px;fill:#cbd5e1}
  .legend{display:flex;gap:10px;flex-wrap:wrap}
  .swatch{width:10px;height:10px;border-radius:2px;display:inline-block;margin-right:6px}
  .grad-label{font-size:12px; fill: var(--ink); paint-order: stroke; stroke: #0b1226; stroke-width: 3px;}
</style>
</head>
<body>
  <div class="app">
    <h1>ML Visual Playground</h1>
    <div class="sub">Tabs for two programs: <strong>Gradient Descent</strong> with on-graph values + stepping, and a <strong>Neural Network</strong> with forward pass <em>and</em> backpropagation steps with numbers drawn on the canvas.</div>

    <div class="tabs" role="tablist" aria-label="Programs">
      <button class="tab" role="tab" aria-selected="true" aria-controls="gd" id="tab-gd">Gradient Descent</button>
      <button class="tab" role="tab" aria-selected="false" aria-controls="nn" id="tab-nn">Neural Net (Fwd + Backprop)</button>
    </div>

    <!-- ===================== PROGRAM 1: GRADIENT DESCENT ===================== -->
    <section id="gd" class="panel" role="tabpanel" aria-labelledby="tab-gd">
      <div class="grid">
        <div>
          <div class="plot-wrap">
            <svg id="gd-svg" viewBox="0 0 900 480" preserveAspectRatio="xMidYMid meet" aria-label="Quadratic curve and gradient descent animation">
              <defs>
                <marker id="arrowHead" markerWidth="8" markerHeight="8" refX="6" refY="4" orient="auto">
                  <polygon points="0 0, 8 4, 0 8" fill="var(--good)"></polygon>
                </marker>
              </defs>
            </svg>
          </div>
          <div class="row" style="justify-content:space-between;margin-top:10px">
            <div class="legend">
              <span class="pill"><span class="swatch" style="background:var(--accent)"></span> f(x)=x²+5</span>
              <span class="pill"><span class="swatch" style="background:var(--accent2)"></span> tangent at x<sub>t</sub></span>
              <span class="pill"><span class="swatch" style="background:var(--good)"></span> update → x<sub>t+1</sub></span>
            </div>
            <div class="row">
              <button id="gd-play">▶ Play</button>
              <button id="gd-step" class="ghost">Step →</button>
              <button id="gd-back" class="ghost">← Back</button>
              <button id="gd-reset" class="ghost">Reset</button>
            </div>
          </div>
        </div>
        <div>
          <div class="control"><label>Learning rate η: <strong id="gd-etaLabel">0.20</strong></label><input id="gd-eta" type="range" min="0.01" max="1" step="0.01" value="0.20" /></div>
          <div class="control"><label>Start x₀: <strong id="gd-x0Label">-8.0</strong></label><input id="gd-x0" type="range" min="-9" max="9" step="0.1" value="-8" /></div>
          <div class="control"><label>Iterations: <strong id="gd-nLabel">20</strong></label><input id="gd-n" type="range" min="1" max="50" step="1" value="20" /></div>
          <div class="control"><label>Speed: <strong id="gd-spdLabel">1×</strong></label><input id="gd-spd" type="range" min="0.25" max="3" step="0.25" value="1" /></div>

          <p class="panel-title">Update math (current step t)</p>
          <div class="control math" id="gd-math" style="line-height:1.6">
            f(x)=x²+5, ∇f(x)=2x<br>
            ∇f(x<sub>t</sub>) = 2·<span id="gd-mx"></span> = <strong id="gd-m"></strong><br>
            x<sub>t+1</sub> = x<sub>t</sub> − <span class="hl">η</span>∇f(x<sub>t</sub>) = <span id="gd-x_t"></span> − <span id="gd-eta_v"></span>·<span id="gd-grad_v"></span> = <strong id="gd-x_next"></strong>
          </div>

          <p class="panel-title">Iterations</p>
          <div class="control" style="max-height:220px;overflow:auto">
            <table class="table" id="gd-table">
              <thead><tr><th>t</th><th>x<sub>t</sub></th><th>∇f</th><th>Δ=η∇f</th><th>f(x)</th></tr></thead>
              <tbody></tbody>
            </table>
          </div>
        </div>
      </div>
    </section>

    <!-- ===================== PROGRAM 2: NEURAL NETWORK (FORWARD + BACKPROP) ===================== -->
    <section id="nn" class="panel" role="tabpanel" aria-labelledby="tab-nn" hidden>
      <div class="grid">
        <div>
          <div class="plot-wrap">
            <svg id="nn-svg" viewBox="0 0 900 480" preserveAspectRatio="xMidYMid meet" aria-label="Neural network forward & backprop"></svg>
          </div>
          <div class="row" style="justify-content:space-between;margin-top:10px">
            <div class="legend">
              <span class="pill"><span class="swatch" style="background:var(--good)"></span> positive weight</span>
              <span class="pill"><span class="swatch" style="background:var(--bad)"></span> negative weight</span>
              <span class="pill">node fill = activation a</span>
            </div>
            <div class="row">
              <button id="nn-step">Step →</button>
              <button id="nn-prev" class="ghost">← Prev</button>
              <button id="nn-forward" class="ghost">Run Forward</button>
              <button id="nn-update" class="ghost">Apply Update</button>
              <button id="nn-random" class="ghost">Randomize Weights</button>
              <button id="nn-reset" class="ghost">Reset</button>
            </div>
          </div>
        </div>
        <div>
          <div class="control"><label>Inputs x (2D)</label>
            <div class="row">
              <div style="min-width:120px">x₁: <input id="x1" type="range" min="-1" max="1" step="0.01" value="0.30" style="width:160px"> <span id="x1v" class="math">0.30</span></div>
              <div style="min-width:120px">x₂: <input id="x2" type="range" min="-1" max="1" step="0.01" value="-0.50" style="width:160px"> <span id="x2v" class="math">-0.50</span></div>
            </div>
          </div>
          <div class="control"><label>Activation φ</label>
            <div class="row">
              <select id="act">
                <option value="sigmoid">sigmoid</option>
                <option value="tanh">tanh</option>
                <option value="relu" selected>ReLU</option>
              </select>
            </div>
          </div>
          <div class="control"><label>Target y* & Learning rate η</label>
            <div class="row">
              <div style="min-width:160px">y*: <input id="yt" type="range" min="-1" max="1" step="0.01" value="0.20" style="width:180px"> <span id="ytv" class="math">0.20</span></div>
              <div style="min-width:160px">η: <input id="nn-eta" type="range" min="0.01" max="1" step="0.01" value="0.10" style="width:180px"> <span id="nn-etav" class="math">0.10</span></div>
            </div>
            <div class="math" style="margin-top:6px">Loss L = ½ (y − y*)²</div>
          </div>

          <p class="panel-title">Math at selected neuron</p>
          <div class="control math" id="nn-math" style="line-height:1.6">Click a hidden/output neuron to see its equation.</div>

          <p class="panel-title">Weights (W) & biases (b)</p>
          <div class="control" id="nn-params" style="max-height:220px;overflow:auto;font-family:ui-monospace,monospace"></div>
        </div>
      </div>
    </section>
  </div>

<script>
/******************** Tabs ********************/ 
const tabs=[...document.querySelectorAll('.tab')];
const panels={ gd:document.getElementById('gd'), nn:document.getElementById('nn') };
tabs.forEach(btn=>btn.addEventListener('click',()=>{
  tabs.forEach(b=>b.setAttribute('aria-selected', String(b===btn)));
  Object.values(panels).forEach(p=>p.hidden=true);
  panels[btn.getAttribute('aria-controls')].hidden=false;
}));

/******************** Program 1: Gradient Descent ********************/
function f(x){ return x*x + 5; }
function df(x){ return 2*x; }

const svgGD = document.getElementById('gd-svg');
const W=900, H=480, padL=48, padR=20, padT=16, padB=48;
const xMin=-10,xMax=10,yMin=0,yMax=110;
const sx=x=> padL + ((x-xMin)/(xMax-xMin))*(W-padL-padR);
const sy=y=> H - padB - ((y-yMin)/(yMax-yMin))*(H-padT-padB);
function addGD(tag,attrs){const e=document.createElementNS('http://www.w3.org/2000/svg',tag);for(const k in attrs)e.setAttribute(k,attrs[k]);svgGD.appendChild(e);return e;}

(function drawAxes(){
  addGD('line',{x1:padL,y1:sy(0),x2:W-padR,y2:sy(0),stroke:'#2a3a57'});
  addGD('line',{x1:padL,y1:padT,x2:padL,y2:H-padB,stroke:'#2a3a57'});
  for(let x=-10;x<=10;x+=2){ addGD('line',{x1:sx(x),y1:padT,x2:sx(x),y2:H-padB, class:'gridline'});
    const t=document.createElementNS('http://www.w3.org/2000/svg','text'); t.setAttribute('x',sx(x));t.setAttribute('y',H-14);t.setAttribute('fill','#64748b');t.setAttribute('font-size','10');t.setAttribute('text-anchor','middle'); t.textContent=x; svgGD.appendChild(t); }
  [0,20,40,60,80,100].forEach(y=>{ addGD('line',{x1:padL,y1:sy(y),x2:W-padR,y2:sy(y), class:'gridline'});
    const t=document.createElementNS('http://www.w3.org/2000/svg','text'); t.setAttribute('x',padL-8);t.setAttribute('y',sy(y)+4);t.setAttribute('fill','#64748b');t.setAttribute('font-size','10');t.setAttribute('text-anchor','end'); t.textContent=y; svgGD.appendChild(t);});
  const xl=document.createElementNS('http://www.w3.org/2000/svg','text'); xl.setAttribute('x',W-26); xl.setAttribute('y',sy(0)-6); xl.setAttribute('fill','#94a3b8'); xl.textContent='x'; svgGD.appendChild(xl);
  const yl=document.createElementNS('http://www.w3.org/2000/svg','text'); yl.setAttribute('x',padL+6); yl.setAttribute('y',padT+12); yl.setAttribute('fill','#94a3b8'); yl.textContent='f(x)'; svgGD.appendChild(yl);
})();
(function drawCurve(){
  let d=''; for(let x=xMin;x<=xMax;x+=0.1){ d += (x===xMin?`M ${sx(x)} ${sy(f(x))}`:` L ${sx(x)} ${sy(f(x))}`); }
  addGD('path',{d, class:'curve'});
})();

const gTangentGD = addGD('path',{class:'tangent'});
const gArrowGD = addGD('line',{class:'arrow'});
const ghostXGD = addGD('line',{class:'ghost-x',x1:0,y1:0,x2:0,y2:0});
const ptGD = addGD('circle',{r:5,class:'point'});
const ptNextGD = addGD('circle',{r:5,class:'next-point',visibility:'hidden'});
const lblX = addGD('text',{class:'label',x:0,y:0});
const lblF = addGD('text',{class:'label',x:0,y:0});
const lblGrad = addGD('text',{class:'label',x:0,y:0});
const lblDelta = addGD('text',{class:'label',x:0,y:0});

function updateGraphicsGD(x, xNext){
  const y=f(x), m=df(x), X=sx(x), Y=sy(y);
  ptGD.setAttribute('cx',X); ptGD.setAttribute('cy',Y);
  const xa=Math.max(xMin,x-3), xb=Math.min(xMax,x+3);
  const d=`M ${sx(xa)} ${sy(y+m*(xa-x))} L ${sx(xb)} ${sy(y+m*(xb-x))}`; gTangentGD.setAttribute('d',d);
  if(Number.isFinite(xNext)){
    const X2=sx(xNext), Y2=sy(f(xNext));
    ptNextGD.setAttribute('cx',X2); ptNextGD.setAttribute('cy',Y2); ptNextGD.setAttribute('visibility','visible');
    gArrowGD.setAttribute('x1',X); gArrowGD.setAttribute('y1',Y); gArrowGD.setAttribute('x2',X2); gArrowGD.setAttribute('y2',Y2);
  }else{ ptNextGD.setAttribute('visibility','hidden'); }
  ghostXGD.setAttribute('x1',X); ghostXGD.setAttribute('x2',X); ghostXGD.setAttribute('y1',sy(0)); ghostXGD.setAttribute('y2',Y);

  // Labels placed near point and arrow tail
  lblX.setAttribute('x', X+10); lblX.setAttribute('y', Y-16); lblX.textContent = `x_t=${x.toFixed(3)}`;
  lblF.setAttribute('x', X+10); lblF.setAttribute('y', Y+2); lblF.textContent = `f(x_t)=${y.toFixed(3)}`;
  const grad=m; lblGrad.setAttribute('x', X-70); lblGrad.setAttribute('y', Y-16); lblGrad.textContent = `∇f=${grad.toFixed(3)}`;
  if(Number.isFinite(xNext)){
    const delta = (x - xNext); // = eta*grad
    lblDelta.setAttribute('x', X-70); lblDelta.setAttribute('y', Y+2); lblDelta.textContent = `Δ=η∇f=${delta.toFixed(3)}`;
  } else { lblDelta.textContent=''; }
}

function computePath(x0,eta,n){ const p=[x0]; let x=x0; for(let i=0;i<n;i++){ x = x - eta*df(x); p.push(Number.isFinite(x)?x:NaN);} return p; }

const elEta=document.getElementById('gd-eta');
const elX0=document.getElementById('gd-x0');
const elN=document.getElementById('gd-n');
const elSpd=document.getElementById('gd-spd');
const labEta=document.getElementById('gd-etaLabel');
const labX0=document.getElementById('gd-x0Label');
const labN=document.getElementById('gd-nLabel');
const labSpd=document.getElementById('gd-spdLabel');
const mathBox={mx:document.getElementById('gd-mx'), m:document.getElementById('gd-m'), x_t:document.getElementById('gd-x_t'), eta_v:document.getElementById('gd-eta_v'), grad_v:document.getElementById('gd-grad_v'), x_next:document.getElementById('gd-x_next')};
const btnPlay=document.getElementById('gd-play');
const btnStep=document.getElementById('gd-step');
const btnBack=document.getElementById('gd-back');
const btnReset=document.getElementById('gd-reset');
const tbodyGD=document.querySelector('#gd-table tbody');

let pathGD=[], tGD=0, playingGD=false, lastTickGD=0, rafGD=null;
function fillTableGD(){ tbodyGD.innerHTML=''; for(let i=0;i<pathGD.length-1;i++){const x=pathGD[i], g=df(x), d=(+elEta.value)*g; const tr=document.createElement('tr'); tr.innerHTML=`<td>${i}</td><td>${x.toFixed(4)}</td><td>${g.toFixed(4)}</td><td>${d.toFixed(4)}</td><td>${f(x).toFixed(4)}</td>`; tbodyGD.appendChild(tr);} }
function highlightRowGD(i){ Array.from(tbodyGD.children).forEach((tr,idx)=>tr.style.background = idx===i?'rgba(56,189,248,.08)':'transparent'); }
function refreshPathGD(){ const x0=+elX0.value, eta=+elEta.value, n=+elN.value; pathGD=computePath(x0,eta,n); tGD=0; fillTableGD(); updateUIGD(); highlightRowGD(-1); }
function updateUIGD(){ labEta.textContent=(+elEta.value).toFixed(2); labX0.textContent=(+elX0.value).toFixed(1); labN.textContent=elN.value; labSpd.textContent=elSpd.value+'×'; const x=pathGD[tGD]; const grad=df(x); const xNext=(tGD<pathGD.length-1)?pathGD[tGD+1]:NaN; mathBox.mx.textContent=x.toFixed(4); mathBox.m.textContent=grad.toFixed(4); mathBox.x_t.textContent=x.toFixed(4); mathBox.eta_v.textContent=(+elEta.value).toFixed(2); mathBox.grad_v.textContent=grad.toFixed(4); mathBox.x_next.textContent=Number.isFinite(xNext)?xNext.toFixed(4):'—'; updateGraphicsGD(x,xNext); }
function stepGD(){ if(tGD<pathGD.length-1){tGD++; updateUIGD(); highlightRowGD(tGD-1);} if(tGD>=pathGD.length-1){ playingGD=false; btnPlay.textContent='▶ Play'; } }
function backGD(){ if(tGD>0){ tGD--; updateUIGD(); highlightRowGD(tGD-1);} }
function loopGD(ts){ if(!playingGD){ rafGD=null; return;} if(!lastTickGD) lastTickGD=ts; const interval=700/(+elSpd.value); if(ts-lastTickGD>=interval){ lastTickGD=ts; stepGD(); } rafGD=requestAnimationFrame(loopGD); }
btnPlay.addEventListener('click',()=>{ playingGD=!playingGD; btnPlay.textContent=playingGD?'⏸ Pause':'▶ Play'; if(playingGD && !rafGD) requestAnimationFrame(loopGD); });
btnStep.addEventListener('click',()=>{ playingGD=false; btnPlay.textContent='▶ Play'; stepGD(); });
btnBack.addEventListener('click',()=>{ playingGD=false; btnPlay.textContent='▶ Play'; backGD(); });
btnReset.addEventListener('click',()=>{ playingGD=false; btnPlay.textContent='▶ Play'; refreshPathGD(); });
[elEta,elX0,elN].forEach(el=>el.addEventListener('input',refreshPathGD));
elSpd.addEventListener('input',updateUIGD);
refreshPathGD();

/******************** Program 2: Neural Network — Forward + Backprop ********************/
const svgNN=document.getElementById('nn-svg');
function addNN(tag,attrs){const e=document.createElementNS('http://www.w3.org/2000/svg',tag);for(const k in attrs)e.setAttribute(k,attrs[k]);svgNN.appendChild(e);return e;}

// Architecture: 2 -> 3 -> 1
const LAYOUT={layers:[2,3,1], xs:[120,450,780], yTop:80, yGap:120};
function nodeY(layerIdx, nodeIdx){ const n=LAYOUT.layers[layerIdx]; const totalH=(n-1)*LAYOUT.yGap; return 240 - totalH/2 + nodeIdx*LAYOUT.yGap; }

// Parameters
let W1=[[0.8,-0.2,0.4],[0.3,0.7,-0.5]]; // (2,3)
let b1=[0.1,-0.2,0.05];
let W2=[[0.6],[ -0.4 ],[0.3]]; // (3,1)
let b2=[0.0];

function randomize(){ W1=Array.from({length:2},()=>Array.from({length:3},()=> (Math.random()-.5)*1.6)); b1=Array.from({length:3},()=> (Math.random()-.5)*0.8); W2=Array.from({length:3},()=>[ (Math.random()-.5)*1.6 ]); b2=[ (Math.random()-.5)*0.8 ]; renderParams(); drawWeights(); forward(true); stage=0; }

function actFn(name,x){ if(name==='relu') return Math.max(0,x); if(name==='tanh') return Math.tanh(x); return 1/(1+Math.exp(-x)); }
function dActFn(name,x){ if(name==='relu') return x>0?1:0; if(name==='tanh') return 1-Math.tanh(x)**2; const s=1/(1+Math.exp(-x)); return s*(1-s); }
function actName(){ return document.getElementById('act').value; }

// Inputs & controls
const x1El=document.getElementById('x1'); const x2El=document.getElementById('x2');
const x1v=document.getElementById('x1v'); const x2v=document.getElementById('x2v');
const ytEl=document.getElementById('yt'); const ytv=document.getElementById('ytv');
const etaNNEl=document.getElementById('nn-eta'); const etaNNv=document.getElementById('nn-etav');
[x1El,x2El].forEach(el=>el.addEventListener('input',()=>{ x1v.textContent=(+x1El.value).toFixed(2); x2v.textContent=(+x2El.value).toFixed(2); forward(true); }));
ytEl.addEventListener('input',()=>{ ytv.textContent=(+ytEl.value).toFixed(2); forward(true); });
etaNNEl.addEventListener('input',()=>{ etaNNv.textContent=(+etaNNEl.value).toFixed(2); });

document.getElementById('nn-random').addEventListener('click',()=>{ randomize(); });
document.getElementById('nn-reset').addEventListener('click',()=>{ W1=[[0.8,-0.2,0.4],[0.3,0.7,-0.5]]; b1=[0.1,-0.2,0.05]; W2=[[0.6],[-0.4],[0.3]]; b2=[0.0]; renderParams(); drawWeights(); forward(true); stage=0; });
document.getElementById('act').addEventListener('change',()=>{ forward(true); stage=0; });

document.getElementById('nn-forward').addEventListener('click',()=> forward());

// Step controls
const btnNNStep=document.getElementById('nn-step');
const btnNNPrev=document.getElementById('nn-prev');
const btnNNUpdate=document.getElementById('nn-update');

// Draw skeleton
let edges=[], nodes=[], labels=[], gradLabels=[];
function drawSkeleton(){
  svgNN.innerHTML=''; edges=[]; nodes=[]; labels=[]; gradLabels=[];
  // edges input->hidden
  for(let i=0;i<2;i++){
    for(let j=0;j<3;j++){
      edges.push(addNN('line',{class:'edge', x1:LAYOUT.xs[0], y1:nodeY(0,i), x2:LAYOUT.xs[1], y2:nodeY(1,j)}));
      gradLabels.push(addNN('text',{class:'grad-label', x:(LAYOUT.xs[0]+LAYOUT.xs[1])/2 - 20, y:(nodeY(0,i)+nodeY(1,j))/2 - 6}));
    }
  }
  // edges hidden->out
  for(let j=0;j<3;j++){
    edges.push(addNN('line',{class:'edge', x1:LAYOUT.xs[1], y1:nodeY(1,j), x2:LAYOUT.xs[2], y2:nodeY(2,0)}));
    gradLabels.push(addNN('text',{class:'grad-label', x:(LAYOUT.xs[1]+LAYOUT.xs[2])/2 + 14, y:(nodeY(1,j)+nodeY(2,0))/2 - 6}));
  }
  // nodes
  for(let i=0;i<2;i++){ nodes.push(addNN('circle',{class:'node', r:18, cx:LAYOUT.xs[0], cy:nodeY(0,i)})); labels.push(addNN('text',{class:'label', x:LAYOUT.xs[0]-34, y:nodeY(0,i)-24})); }
  for(let j=0;j<3;j++){ nodes.push(addNN('circle',{class:'node', r:22, cx:LAYOUT.xs[1], cy:nodeY(1,j), 'data-layer':'h', 'data-idx':j, style:'cursor:pointer'})); labels.push(addNN('text',{class:'label', x:LAYOUT.xs[1]-34, y:nodeY(1,j)-28})); }
  nodes.push(addNN('circle',{class:'node', r:24, cx:LAYOUT.xs[2], cy:nodeY(2,0), 'data-layer':'o', 'data-idx':0, style:'cursor:pointer'}));
  labels.push(addNN('text',{class:'label', x:LAYOUT.xs[2]-34, y:nodeY(2,0)-30}));
  // headers
  const lab = (x,y,t)=>{ const tx=addNN('text',{x,y,fill:'#cbd5e1','font-size':'12','text-anchor':'middle'}); tx.textContent=t; };
  lab(LAYOUT.xs[0], 30, 'Inputs x'); lab(LAYOUT.xs[1], 30, 'Hidden h'); lab(LAYOUT.xs[2], 30, 'Output y');
}

function edgeWidth(w){ return 0.8 + Math.min(2.8, Math.abs(w)*1.2); }

function drawWeights(){
  let k=0;
  // input->hidden
  for(let i=0;i<2;i++){
    for(let j=0;j<3;j++){
      const e=edges[k++]; const w=W1[i][j]; e.setAttribute('class', 'edge '+(w>=0?'pos':'neg')); e.setAttribute('stroke-width', edgeWidth(w));
    }
  }
  // hidden->out
  for(let j=0;j<3;j++){
    const e=edges[k++]; const w=W2[j][0]; e.setAttribute('class','edge '+(w>=0?'pos':'neg')); e.setAttribute('stroke-width', edgeWidth(w));
  }
}

function drawNodes(aIn=[+x1El.value,+x2El.value], aH=[0,0,0], y=0, z1=[0,0,0], z2=0){
  // input activations tint + labels
  for(let i=0;i<2;i++){
    const v=aIn[i]; const intensity=Math.min(1, Math.abs(v)); nodes[i].setAttribute('fill',`rgba(56,189,248,${0.10+0.55*intensity})`);
    labels[i].textContent = `x${i+1}=${v.toFixed(3)}`;
  }
  // hidden nodes + labels
  for(let j=0;j<3;j++){
    const a=aH[j]; nodes[2+j].setAttribute('fill',`rgba(245,158,11,${0.10+0.55*Math.min(1,Math.abs(a))})`);
    labels[2+j].textContent = `z${j+1}=${z1[j].toFixed(3)}\na${j+1}=${a.toFixed(3)}`;
  }
  // output node + label
  labels[labels.length-1].textContent = `z=${z2.toFixed(3)}\ny=${y.toFixed(3)}`;
  nodes[nodes.length-1].setAttribute('fill',`rgba(34,197,94,${0.10+0.55*Math.min(1,Math.abs(y))})`);
}

function renderParams(){
  const box=document.getElementById('nn-params');
  let html='<div class="math">';
  html += `<div>W1 (2×3) = [[${W1[0].map(n=>n.toFixed(3)).join(', ')}], [${W1[1].map(n=>n.toFixed(3)).join(', ')}]]</div>`;
  html += `<div>b1 (3) = [${b1.map(n=>n.toFixed(3)).join(', ')}]</div>`;
  html += `<div>W2 (3×1) = [[${W2.map(r=>r[0].toFixed(3)).join(', ')}]]</div>`;
  html += `<div>b2 (1) = [${b2[0].toFixed(3)}]</div>`;
  html += '</div>';
  box.innerHTML=html;
}

// Forward pass cache
let cache={x:[0,0], z1:[0,0,0], a1:[0,0,0], z2:0, y:0, act:'relu'};
function forward(skipAnim=false){
  const x=[+x1El.value, +x2El.value];
  const act=actName();
  const z1=[0,0,0];
  for(let j=0;j<3;j++) z1[j]=W1[0][j]*x[0] + W1[1][j]*x[1] + b1[j];
  const a1=z1.map(v=>actFn(act,v));
  const z2=W2[0][0]*a1[0] + W2[1][0]*a1[1] + W2[2][0]*a1[2] + b2[0];
  const y=actFn(act,z2);
  cache={x, z1, a1, z2, y, act};
  drawNodes(x,a1,y,z1,z2);
  drawWeights();
  // pulse animation
  if(!skipAnim){
    const idxs=[0,1,2,3,4,5,6,7,8];
    idxs.forEach((i,ii)=>{ setTimeout(()=>{ edges[i].animate([{opacity:1},{opacity:.2},{opacity:1}],{duration:400,iterations:1}); }, ii*80); });
  }
  // enable neuron click explanations
  nodes.forEach(n=> n.onclick=null);
  for(let j=0;j<3;j++){
    nodes[2+j].onclick=()=> setMathPanel('h',j);
  }
  nodes[nodes.length-1].onclick=()=> setMathPanel('o',0);
}

function setMathPanel(layer, idx){
  const box=document.getElementById('nn-math');
  const {x,z1,a1,z2,y,act}=cache;
  if(layer==='h'){
    const [x1,x2]=x; const z=z1[idx], a=a1[idx];
    const line = `z = W₁ᵀx + b₁ = (${W1[0][idx].toFixed(3)}·${x1.toFixed(3)} + ${W1[1][idx].toFixed(3)}·${x2.toFixed(3)} + ${b1[idx].toFixed(3)}) = <strong>${z.toFixed(4)}</strong>`;
    const aLine = `a = φ(z) with φ=${act} ⇒ <strong>${a.toFixed(4)}</strong>`;
    box.innerHTML = `<div class="math">Hidden neuron h<sub>${idx+1}</sub><br>${line}<br>${aLine}</div>`;
  } else {
    const line = `z = W₂ᵀa₁ + b₂ = (${W2[0][0].toFixed(3)}·a₁₁ + ${W2[1][0].toFixed(3)}·a₁₂ + ${W2[2][0].toFixed(3)}·a₁₃ + ${b2[0].toFixed(3)}) = <strong>${z2.toFixed(4)}</strong>`;
    const a = `y = φ(z) with φ=${act} ⇒ <strong>${y.toFixed(4)}</strong>`;
    box.innerHTML = `<div class="math">Output neuron y<br>${line}<br>${a}</div>`;
  }
}

// ===== Backprop (step-by-step) =====
let stage = 0; // 0: forward done, 1: loss, 2: delta_out, 3: delta_hidden, 4: gradients, 5: update applied
let grads={dW1:[[0,0,0],[0,0,0]], db1:[0,0,0], dW2:[[0],[0],[0]], db2:[0], deltas:{dh:[0,0,0], do:0}, loss:0};

function computeBackprop(){
  const {x,z1,a1,z2,y,act}=cache; const yT=+ytEl.value; const eta=+etaNNEl.value;
  const dphi_out=dActFn(act,z2); const err = (y - yT); const loss = 0.5*err*err; // MSE 1/2
  const delta_o = err * dphi_out; // dL/dz2
  const dh=[0,0,0];
  for(let j=0;j<3;j++){ dh[j] = (W2[j][0]*delta_o) * dActFn(act,z1[j]); }
  const dW2=[[a1[0]*delta_o],[a1[1]*delta_o],[a1[2]*delta_o]]; const db2=[delta_o];
  const dW1=[[ x[0]*dh[0], x[0]*dh[1], x[0]*dh[2] ],[ x[1]*dh[0], x[1]*dh[1], x[1]*dh[2] ]];
  const db1=[ dh[0], dh[1], dh[2] ];
  grads={dW1, db1, dW2, db2, deltas:{dh, do:delta_o}, loss, eta};
}

function drawGradLabels(clearOnly=false){
  // place gradient labels near edges
  let k=0; const fmt=v=>Number(v).toFixed(3);
  for(let i=0;i<2;i++){
    for(let j=0;j<3;j++){
      gradLabels[k].textContent = clearOnly?'':`∂L/∂w=${fmt(grads.dW1[i][j])}`; k++; }
  }
  for(let j=0;j<3;j++){ gradLabels[k].textContent = clearOnly?'':`∂L/∂w=${fmt(grads.dW2[j][0])}`; k++; }
}

function drawDeltaNodeBadges(show){
  // add small badges for deltas at nodes
  // hidden deltas over hidden labels line 2, output over output label
  if(show){
    for(let j=0;j<3;j++) labels[2+j].textContent += `\nδh${j+1}=${grads.deltas.dh[j].toFixed(3)}`;
    labels[labels.length-1].textContent += `\nδo=${grads.deltas.do.toFixed(3)}`;
  }
}

function applyUpdate(){
  const eta=+etaNNEl.value;
  for(let i=0;i<2;i++) for(let j=0;j<3;j++) W1[i][j] -= eta*grads.dW1[i][j];
  for(let j=0;j<3;j++) b1[j] -= eta*grads.db1[j];
  for(let j=0;j<3;j++) W2[j][0] -= eta*grads.dW2[j][0];
  b2[0] -= eta*grads.db2[0];
  renderParams();
}

function clearBackpropOverlays(){ drawGradLabels(true); forward(true); }

function stepOnce(dir=+1){
  // dir=+1 forward, dir=-1 backward (previous)
  stage = Math.max(0, Math.min(5, stage + dir));
  const box=document.getElementById('nn-math');
  const {y}=cache; const yT=+ytEl.value;
  if(stage===0){ clearBackpropOverlays(); box.innerHTML = `<div class="math">Forward ready. Click <strong>Step</strong> to compute loss.</div>`; return; }
  if(stage===1){ clearBackpropOverlays(); const err=y - yT; const L=0.5*err*err; grads.loss=L; box.innerHTML = `<div class="math">Loss: L = ½ (y − y*)² = ½ (${y.toFixed(3)} − ${yT.toFixed(3)})² = <strong>${L.toFixed(6)}</strong></div>`; return; }
  if(stage===2){ computeBackprop(); clearBackpropOverlays(); drawDeltaNodeBadges(true); box.innerHTML = `<div class="math">Output delta: δ<sub>o</sub> = (y−y*) φ'(z) = <strong>${grads.deltas.do.toFixed(6)}</strong></div>`; return; }
  if(stage===3){ clearBackpropOverlays(); drawDeltaNodeBadges(true); box.innerHTML = `<div class="math">Hidden deltas δ<sub>h</sub><br>δ = (W₂ δ<sub>o</sub>) ⊙ φ'(z) = [${grads.deltas.dh.map(v=>v.toFixed(6)).join(', ')}]</div>`; return; }
  if(stage===4){ // show gradients on edges
    drawGradLabels(false);
    box.innerHTML = `<div class="math">Gradients on edges shown: ∂L/∂w and ∂L/∂b. Use <strong>Apply Update</strong> to update weights (w ← w − η∂L/∂w).</div>`; return; }
  if(stage===5){ applyUpdate(); forward(true); box.innerHTML = `<div class="math ok">Weights updated with η=${(+etaNNEl.value).toFixed(2)}. Step again to recompute loss.</div>`; return; }
}

btnNNStep.addEventListener('click',()=> stepOnce(+1));
btnNNPrev.addEventListener('click',()=> stepOnce(-1));
btnNNUpdate.addEventListener('click',()=> { stage=4; stepOnce(+1); });

function initNN(){ drawSkeleton(); drawWeights(); renderParams(); forward(true); stage=0; document.getElementById('nn-math').innerHTML = '<div class="math">Forward ready. Click <strong>Step</strong> to compute loss.</div>'; }
initNN();

</script>
</body>
</html>
